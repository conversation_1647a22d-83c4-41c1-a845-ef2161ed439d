#NDK_TOOLCHAIN_VERSION := clang
#NDK_TOOLCHAIN_VERSION := clang-ollvm3.6.1
APP_ABI := armeabi armeabi-v7a arm64-v8a x86 x86_64
APP_PLATFORM := android-19
APP_STL := c++_static

APP_CPPFLAGS += -s -fpermissive -ffunction-sections -fdata-sections -fvisibility=hidden -fvisibility-inlines-hidden 

RELEASE_BUILD := true
ifeq ($(RELEASE_BUILD), true)
    #open log
    APP_CFLAGS += -DLOGGER_ON
    #print method name and line numbers
    APP_CFLAGS += -DLOGGER_DETAIL
    
    #Open the antidebug info
    APP_CFLAGS += -DENABLE_ANTIDEBUG_DEBUG
endif

ANTI_DEBUG := true
ifeq ($(ANTI_DEBUG), true)
    #Anti Debug and Port Detect
    APP_CFLAGS += -DANTI_DEBUG
    APP_CFLAGS += -DANTI_PORT
endif

#Protected SO
PROTECTED_SO := true
ifeq ($(PROTECTED_SO), true)
    APP_CFLAGS += -DPROTECTED_SO
endif

#Method name obfuscutor
METHOD_NAME_OBFUSCUTOR := true
ifeq ($(METHOD_NAME_OBFUSCUTOR), true)
    APP_CFLAGS += -DMETHOD_NAME_OBFUSCUTOR
endif

#Anti Emulator
ANTI_EMULATOR := true
ifeq ($(ANTI_EMULATOR), true)
    APP_CFLAGS += -DANTI_EMULATOR
endif

#Anti Xposed
ANTI_XPOSED := true
ifeq ($(ANTI_XPOSED), true)
    APP_CFLAGS += -DANTI_XPOSED
endif

#Anti ZJDroid
ANTI_ZJDROID := true
ifeq ($(ANTI_ZJDROID), true)
    APP_CFLAGS += -DANTI_ZJDROID
endif

#Anti Substrate(Cydia)
ANTI_SUBSTRATE := false
ifeq ($(ANTI_SUBSTRATE), true)
    APP_CFLAGS += -DANTI_SUBSTRATE
endif

#Anti virtual app
ANTI_VIRTUALAPP := true
ifeq ($(ANTI_VIRTUALAPP), true)
    APP_CFLAGS += -DANTI_VIRTUALAPP
endif

#Anti DexHunter Indroid
DUMP_DEX := true
ifeq ($(DUMP_DEX), true)
    APP_CFLAGS += -DDUMP_DEX
endif
