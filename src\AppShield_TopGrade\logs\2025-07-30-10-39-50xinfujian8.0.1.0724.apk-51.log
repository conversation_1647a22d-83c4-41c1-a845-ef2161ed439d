07/30/2025 10:39:50 [DEBUG] [filename:create_tmp_dir.py] [line:28] 539284d6429920c4
07/30/2025 10:39:50 [DEBUG] [filename:create_tmp_dir.py] [line:46] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\539284d6429920c4
07/30/2025 10:39:50 [DEBUG] [filename:create_tmp_dir.py] [line:16] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\539284d6429920c4 creat success!
07/30/2025 10:39:50 [INFO] [filename:main.py] [line:1520] create the tmp dir success
07/30/2025 10:39:50 [DEBUG] [filename:create_tmp_dir.py] [line:28] 82e01db4aa25dd2c
07/30/2025 10:39:50 [DEBUG] [filename:substitute_apk.py] [line:22] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\539284d6429920c4\82e01db4aa25dd2c.apk
07/30/2025 10:39:50 [INFO] [filename:main.py] [line:269] generate the substitute_apk success
07/30/2025 10:39:50 [DEBUG] [filename:signature_check.py] [line:62] java -cp D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\getCert.jar;D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\support-v4-19.1.0.jar com.jiagu.CollectCert D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\539284d6429920c4\82e01db4aa25dd2c.apk
07/30/2025 10:39:50 [DEBUG] [filename:execute_cmd.py] [line:49] -963500836


07/30/2025 10:39:50 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 10:39:50 [DEBUG] [filename:signature_check.py] [line:102] encrypt_signature_info,the value before encryption is:-963500836,the encrpted value is:	

07/30/2025 10:39:50 [ERROR] [filename:signature_check.py] [line:106] add PK file to the apk assets failed
07/30/2025 10:39:50 [ERROR] [filename:main.py] [line:302] save the app signature into PK failed
