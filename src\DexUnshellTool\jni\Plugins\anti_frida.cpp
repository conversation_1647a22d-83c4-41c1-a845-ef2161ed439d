#include <string>
#include <dirent.h>
#include <errno.h>
#include <fcntl.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>

#include <android/log.h>
#include <sys/syscall.h>
#include <sys/system_properties.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/inotify.h>
#include <sys/select.h>

#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#include "anti_frida.h"
#include "TfdLog.h"

#define MAX_LENGTH 256
static const char *FRIDA_THREAD_GMAIN = "gmain";
static const char *FRIDA_NAMEDPIPE_LINJECTOR = "linjector";
static const char *PROC_STATUS = "/proc/self/task/%s/status";
static const char *PROC_FD = "/proc/self/fd";
static const char *PROC_TASK = "/proc/self/task";

static ssize_t read_one_line(int fd, char *buf, unsigned int max_len) {
    char b;
    ssize_t ret;
    ssize_t bytes_read = 0;

    memset(buf, 0, max_len);
    do {
        ret = read(fd, &b, 1);
        if (ret != 1) {
            if (bytes_read == 0) {
                // error or EOF
                return -1;
            } else {
                return bytes_read;
            }
        }
        if (b == '\n') {
            return bytes_read;
        }
        *(buf++) = b;
        bytes_read += 1;
    } while (bytes_read < max_len - 1);
    return bytes_read;
}

static void detect_frida_namedpipe() {
    frida_check_port();
    DIR *dir = opendir(PROC_FD);
    if (dir != NULL) {
        struct dirent *entry = NULL;
        while ((entry = readdir(dir)) != NULL) {
            struct stat filestat;
            char buf[MAX_LENGTH] = "";
            char filePath[MAX_LENGTH] = "";
            snprintf(filePath, sizeof(filePath), "/proc/self/fd/%s", entry->d_name);
            lstat(filePath, &filestat);
            if ((filestat.st_mode & S_IFMT) == S_IFLNK) {
                //TODO: Another way is to check if filepath belongs to a path not related to system or the app
                // syscall(__NR_openat, AT_FDCWD, filePath, O_RDONLY | O_CLOEXEC, 0);
                // __NR_readlinkat
                syscall(__NR_readlinkat, AT_FDCWD, filePath, buf, MAX_LENGTH);
                // readlinkat(AT_FDCWD, filePath, buf, MAX_LENGTH);
                if (NULL != strstr(buf, FRIDA_NAMEDPIPE_LINJECTOR)) {
                    TFD_LOGI("anti frida success");
                    kill(getpid(), SIGKILL);
                }
            }

        }
    }
    closedir(dir);
}

void detect_frida_threads() {
    DIR *dir = opendir(PROC_TASK);
    struct dirent *entry = NULL;
    while ((entry = readdir(dir)) != NULL) {
        char filePath[MAX_LENGTH] = "";
        if (0 == strcmp(entry->d_name, ".") || 0 == strcmp(entry->d_name, "..")) {
            continue;
        }
        TFD_LOGI("while insert ");
        snprintf(filePath, sizeof(filePath), PROC_STATUS, entry->d_name);
        int fd = syscall(__NR_openat, AT_FDCWD, filePath, O_RDONLY | O_CLOEXEC, 0);
        if (fd != 0) {
            char buf[MAX_LENGTH] = "";
            read_one_line(fd, buf, MAX_LENGTH);
            TFD_LOGI("read_one_line insert ");
            if (strstr(buf, FRIDA_NAMEDPIPE_LINJECTOR) || strstr(buf, FRIDA_THREAD_GMAIN)) {
                TFD_LOGI("anti frida success");
                kill(getpid(), SIGKILL);
            }
            close(fd);
        }
    }
}

void frida_check_port(){
    struct sockaddr_in sa;
    memset(&sa, 0, sizeof(sa));
    sa.sin_family = AF_INET;
    inet_aton("127.0.0.1", &(sa.sin_addr));
    
    int sock;
    sock = socket(AF_INET, SOCK_STREAM, 0);
    sa.sin_port = htons(27042);
    if (connect(sock, (struct sockaddr*)&sa, sizeof(sa)) != -1) {
        close(sock);
        TFD_LOGI("anti frida success");
        kill(getpid(), SIGKILL);
    }
    close(sock);
}
void detect_frida_maps(){
    int pid = getpid();
    FILE *fp;
    char filename[64];
    char line[1024];
    struct timespec sleep_time;
    sleep_time.tv_sec = 2;  // 2秒检测间隔
    sleep_time.tv_nsec = 0;

    if (pid < 0) {
        snprintf(filename, sizeof(filename), "/proc/self/maps");
    }else{
        snprintf(filename, sizeof(filename), "/proc/%d/maps", pid);
    }

    TFD_LOGI("Starting continuous frida maps detection: %s", filename);

    while (1) {
        fp = fopen(filename, "r");
        if (fp != NULL) {
            while (fgets(line, sizeof(line), fp)) {
                if (strstr(line, "frida-agent") || strstr(line, "frida-agent-32") || strstr(line, "frida-agent-64")) {
                    TFD_LOGI("Frida agent detected in maps: %s", line);
                    fclose(fp);
                    exit(0);
                }
            }
            fclose(fp);
        } else {
            TFD_LOGI("Failed to open maps file");
        }

        nanosleep(&sleep_time, NULL);
    }
}

void fridaServerDetection(){
    struct timespec timereq;
    timereq.tv_sec = 5;
    timereq.tv_nsec = 0;
    frida_check_port();
    detect_frida_maps();
    while (1) {
        // detect_frida_threads();
        detect_frida_namedpipe();
        nanosleep(&timereq, NULL);
    }
}

void antiFrida(){
    
    pthread_t t;
    
    int re = pthread_create(&t, NULL, (void*(*)(void*))fridaServerDetection, (void*)NULL);
    
    if(re < 0) {
        TFD_LOGI("failed to create antiFrida thread");
        return;
    }

    pthread_detach(t);
}