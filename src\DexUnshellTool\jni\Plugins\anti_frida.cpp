#include <string>
#include <string.h>
#include <dirent.h>
#include <errno.h>
#include <fcntl.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>

#include <android/log.h>
#include <sys/syscall.h>
#include <sys/system_properties.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/inotify.h>
#include <sys/select.h>

#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

#include "anti_frida.h"
#include "TfdLog.h"

#define MAX_LENGTH 256
static const char *FRIDA_THREAD_GMAIN = "gmain";
static const char *FRIDA_NAMEDPIPE_LINJECTOR = "linjector";
static const char *PROC_STATUS = "/proc/self/task/%s/status";
static const char *PROC_FD = "/proc/self/fd";
static const char *PROC_TASK = "/proc/self/task";

static ssize_t read_one_line(int fd, char *buf, unsigned int max_len) {
    char b;
    ssize_t ret;
    ssize_t bytes_read = 0;

    memset(buf, 0, max_len);
    do {
        ret = read(fd, &b, 1);
        if (ret != 1) {
            if (bytes_read == 0) {
                // error or EOF
                return -1;
            } else {
                return bytes_read;
            }
        }
        if (b == '\n') {
            return bytes_read;
        }
        *(buf++) = b;
        bytes_read += 1;
    } while (bytes_read < max_len - 1);
    return bytes_read;
}

static void detect_frida_namedpipe() {
    frida_check_port();
    DIR *dir = opendir(PROC_FD);
    if (dir != NULL) {
        struct dirent *entry = NULL;
        while ((entry = readdir(dir)) != NULL) {
            struct stat filestat;
            char buf[MAX_LENGTH] = "";
            char filePath[MAX_LENGTH] = "";
            snprintf(filePath, sizeof(filePath), "/proc/self/fd/%s", entry->d_name);
            lstat(filePath, &filestat);
            if ((filestat.st_mode & S_IFMT) == S_IFLNK) {
                //TODO: Another way is to check if filepath belongs to a path not related to system or the app
                // syscall(__NR_openat, AT_FDCWD, filePath, O_RDONLY | O_CLOEXEC, 0);
                // __NR_readlinkat
                syscall(__NR_readlinkat, AT_FDCWD, filePath, buf, MAX_LENGTH);
                // readlinkat(AT_FDCWD, filePath, buf, MAX_LENGTH);
                if (NULL != strstr(buf, FRIDA_NAMEDPIPE_LINJECTOR)) {
                    TFD_LOGI("anti frida success");
                    kill(getpid(), SIGKILL);
                }
            }

        }
    }
    closedir(dir);
}

void detect_frida_threads() {
    DIR *dir = opendir(PROC_TASK);
    struct dirent *entry = NULL;
    while ((entry = readdir(dir)) != NULL) {
        char filePath[MAX_LENGTH] = "";
        if (0 == strcmp(entry->d_name, ".") || 0 == strcmp(entry->d_name, "..")) {
            continue;
        }
        TFD_LOGI("while insert ");
        snprintf(filePath, sizeof(filePath), PROC_STATUS, entry->d_name);
        int fd = syscall(__NR_openat, AT_FDCWD, filePath, O_RDONLY | O_CLOEXEC, 0);
        if (fd != 0) {
            char buf[MAX_LENGTH] = "";
            read_one_line(fd, buf, MAX_LENGTH);
            TFD_LOGI("read_one_line insert ");
            if (strstr(buf, FRIDA_NAMEDPIPE_LINJECTOR) || strstr(buf, FRIDA_THREAD_GMAIN)) {
                TFD_LOGI("anti frida success");
                kill(getpid(), SIGKILL);
            }
            close(fd);
        }
    }
}

void frida_check_port(){
    struct sockaddr_in sa;
    memset(&sa, 0, sizeof(sa));
    sa.sin_family = AF_INET;
    inet_aton("127.0.0.1", &(sa.sin_addr));
    
    int sock;
    sock = socket(AF_INET, SOCK_STREAM, 0);
    sa.sin_port = htons(27042);
    if (connect(sock, (struct sockaddr*)&sa, sizeof(sa)) != -1) {
        close(sock);
        TFD_LOGI("anti frida success");
        kill(getpid(), SIGKILL);
    }
    close(sock);
}
// 检查maps文件内容是否包含frida相关内容
static int check_maps_content(const char *filename) {
    FILE *fp;
    char line[1024];

    fp = fopen(filename, "r");
    if (fp == NULL) {
        TFD_LOGI("无法打开maps文件: %s", filename);
        return 0;
    }

    while (fgets(line, sizeof(line), fp)) {
        if (strstr(line, "frida-agent") ||
            strstr(line, "frida-agent-32") ||
            strstr(line, "frida-agent-64") ||
            strstr(line, "frida") ||
            strstr(line, "gum-js-loop") ||
            strstr(line, "gmain")) {
            TFD_LOGI("在maps中检测到Frida相关内容: %s", line);
            fclose(fp);
            return 1;
        }
    }

    fclose(fp);
    return 0;
}

void detect_frida_maps(){
    int pid = getpid();
    char filename[64];
    int inotify_fd, watch_fd;
    char buffer[4096];
    struct inotify_event *event;
    fd_set readfds;
    struct timeval timeout;
    int select_result;

    if (pid < 0) {
        snprintf(filename, sizeof(filename), "/proc/self/maps");
    } else {
        snprintf(filename, sizeof(filename), "/proc/%d/maps", pid);
    }

    TFD_LOGI("开始使用inotify监控maps文件变化: %s", filename);

    // 创建inotify实例
    inotify_fd = inotify_init();
    if (inotify_fd < 0) {
        TFD_LOGI("创建inotify实例失败: %s", strerror(errno));
        return;
    }

    // 监控maps文件的修改和访问事件
    watch_fd = inotify_add_watch(inotify_fd, filename, IN_MODIFY | IN_ACCESS | IN_OPEN);
    if (watch_fd < 0) {
        TFD_LOGI("添加inotify监控失败: %s, 错误: %s", filename, strerror(errno));
        close(inotify_fd);
        return;
    }

    TFD_LOGI("成功设置inotify监控，监控描述符: %d", watch_fd);

    // 首次检查maps文件内容
    if (check_maps_content(filename)) {
        TFD_LOGI("初始检查发现Frida，程序退出");
        inotify_rm_watch(inotify_fd, watch_fd);
        close(inotify_fd);
        kill(getpid(), SIGKILL);
        return;
    }

    while (1) {
        FD_ZERO(&readfds);
        FD_SET(inotify_fd, &readfds);

        // 设置超时时间为5秒，避免无限阻塞
        timeout.tv_sec = 5;
        timeout.tv_usec = 0;

        select_result = select(inotify_fd + 1, &readfds, NULL, NULL, &timeout);

        if (select_result < 0) {
            TFD_LOGI("select调用失败: %s", strerror(errno));
            break;
        } else if (select_result == 0) {
            // 超时，继续监控
            TFD_LOGI("inotify监控超时，继续监控...");
            continue;
        }

        if (FD_ISSET(inotify_fd, &readfds)) {
            ssize_t length = read(inotify_fd, buffer, sizeof(buffer));
            if (length < 0) {
                TFD_LOGI("读取inotify事件失败: %s", strerror(errno));
                break;
            }

            // 处理inotify事件
            for (char *ptr = buffer; ptr < buffer + length; ) {
                event = (struct inotify_event *)ptr;

                if (event->mask & IN_MODIFY) {
                    TFD_LOGI("检测到maps文件修改事件");
                } else if (event->mask & IN_ACCESS) {
                    TFD_LOGI("检测到maps文件访问事件");
                } else if (event->mask & IN_OPEN) {
                    TFD_LOGI("检测到maps文件打开事件");
                }

                // 检查maps文件内容
                if (check_maps_content(filename)) {
                    TFD_LOGI("inotify监控检测到Frida，程序退出");
                    inotify_rm_watch(inotify_fd, watch_fd);
                    close(inotify_fd);
                    kill(getpid(), SIGKILL);
                    return;
                }

                ptr += sizeof(struct inotify_event) + event->len;
            }
        }
    }

    // 清理资源
    TFD_LOGI("停止inotify监控");
    inotify_rm_watch(inotify_fd, watch_fd);
    close(inotify_fd);
}

void fridaServerDetection(){
    struct timespec timereq;
    timereq.tv_sec = 5;
    timereq.tv_nsec = 0;
    frida_check_port();
    detect_frida_maps();
    while (1) {
        // detect_frida_threads();
        detect_frida_namedpipe();
        nanosleep(&timereq, NULL);
    }
}

void antiFrida(){
    
    pthread_t t;
    
    int re = pthread_create(&t, NULL, (void*(*)(void*))fridaServerDetection, (void*)NULL);
    
    if(re < 0) {
        TFD_LOGI("failed to create antiFrida thread");
        return;
    }

    pthread_detach(t);
}