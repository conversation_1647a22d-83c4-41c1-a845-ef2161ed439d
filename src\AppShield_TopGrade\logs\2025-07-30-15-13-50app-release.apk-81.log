07/30/2025 15:13:50 [DEBUG] [filename:create_tmp_dir.py] [line:28] 91c18af0aca0af9c
07/30/2025 15:13:50 [DEBUG] [filename:create_tmp_dir.py] [line:46] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c
07/30/2025 15:13:50 [DEBUG] [filename:create_tmp_dir.py] [line:16] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c creat success!
07/30/2025 15:13:50 [INFO] [filename:main.py] [line:1520] create the tmp dir success
07/30/2025 15:13:50 [DEBUG] [filename:create_tmp_dir.py] [line:28] 7379dd241496c4ed
07/30/2025 15:13:50 [DEBUG] [filename:substitute_apk.py] [line:22] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk
07/30/2025 15:13:50 [INFO] [filename:main.py] [line:269] generate the substitute_apk success
07/30/2025 15:13:50 [DEBUG] [filename:main.py] [line:313] The Apk has 1 dex file(s)
07/30/2025 15:13:50 [DEBUG] [filename:filter_architecture.py] [line:82] Filter Architecture Success
07/30/2025 15:13:50 [INFO] [filename:main.py] [line:324] Filter architecture success!
07/30/2025 15:13:50 [DEBUG] [filename:apk.py] [line:116] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk
07/30/2025 15:13:50 [DEBUG] [filename:apk.py] [line:117] .dex
07/30/2025 15:13:50 [DEBUG] [filename:apk.py] [line:121] classes.dex
07/30/2025 15:13:50 [DEBUG] [filename:aapt.py] [line:252] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\tools\apktool\aapt.exe dump xmltree D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk AndroidManifest.xml
07/30/2025 15:13:51 [DEBUG] [filename:execute_cmd.py] [line:49] N: android=http://schemas.android.com/apk/res/android

  E: manifest (line=2)

    A: android:versionCode(0x0101021b)=(type 0x10)0x1

    A: android:versionName(0x0101021c)="1.0" (Raw: "1.0")

    A: android:compileSdkVersion(0x01010572)=(type 0x10)0x22

    A: android:compileSdkVersionCodename(0x01010573)="14" (Raw: "14")

    A: package="com.c2hapmll.tools" (Raw: "com.c2hapmll.tools")

    A: platformBuildVersionCode=(type 0x10)0x22

    A: platformBuildVersionName=(type 0x10)0xe

    E: uses-sdk (line=7)

      A: android:minSdkVersion(0x0101020c)=(type 0x10)0x1a

      A: android:targetSdkVersion(0x01010270)=(type 0x10)0x22

    E: uses-permission (line=12)

      A: android:name(0x01010003)="android.permission.BLUETOOTH" (Raw: "android.permission.BLUETOOTH")

    E: uses-permission (line=13)

      A: android:name(0x01010003)="android.permission.BLUETOOTH_ADMIN" (Raw: "android.permission.BLUETOOTH_ADMIN")

    E: uses-permission (line=14)

      A: android:name(0x01010003)="android.permission.ACCESS_FINE_LOCATION" (Raw: "android.permission.ACCESS_FINE_LOCATION")

    E: uses-permission (line=15)

      A: android:name(0x01010003)="android.permission.BLUETOOTH_SCAN" (Raw: "android.permission.BLUETOOTH_SCAN")

    E: uses-permission (line=16)

      A: android:name(0x01010003)="android.permission.BLUETOOTH_CONNECT" (Raw: "android.permission.BLUETOOTH_CONNECT")

    E: uses-permission (line=17)

      A: android:name(0x01010003)="android.permission.BLUETOOTH_ADVERTISE" (Raw: "android.permission.BLUETOOTH_ADVERTISE")

    E: uses-feature (line=19)

      A: android:name(0x01010003)="android.hardware.bluetooth" (Raw: "android.hardware.bluetooth")

      A: android:required(0x0101028e)=(type 0x12)0xffffffff

    E: permission (line=23)

      A: android:name(0x01010003)="com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" (Raw: "com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION")

      A: android:protectionLevel(0x01010009)=(type 0x11)0x2

    E: uses-permission (line=27)

      A: android:name(0x01010003)="com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" (Raw: "com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION")

    E: application (line=29)

      A: android:theme(0x01010000)=@0x7f100272

      A: android:label(0x01010001)=@0x7f0f001c

      A: android:icon(0x01010002)=@0x7f0d0000

      A: android:allowBackup(0x01010280)=(type 0x12)0xffffffff

      A: android:supportsRtl(0x010103af)=(type 0x12)0xffffffff

      A: android:extractNativeLibs(0x010104ea)=(type 0x12)0x0

      A: android:fullBackupContent(0x010104eb)=@0x7f120000

      A: android:roundIcon(0x0101052c)=@0x7f0d0002

      A: android:appComponentFactory(0x0101057a)="androidx.core.app.CoreComponentFactory" (Raw: "androidx.core.app.CoreComponentFactory")

      A: android:dataExtractionRules(0x0101063e)=@0x7f120001

      E: activity (line=40)

        A: android:name(0x01010003)="com.c2hapmll.tools.MainActivity" (Raw: "com.c2hapmll.tools.MainActivity")

        A: android:exported(0x01010010)=(type 0x12)0xffffffff

        E: intent-filter (line=43)

          E: action (line=44)

            A: android:name(0x01010003)="android.intent.action.MAIN" (Raw: "android.intent.action.MAIN")

          E: category (line=46)

            A: android:name(0x01010003)="android.intent.category.LAUNCHER" (Raw: "android.intent.category.LAUNCHER")

      E: activity (line=49)

        A: android:name(0x01010003)="com.c2hapmll.tools.DeveloperActivity" (Raw: "com.c2hapmll.tools.DeveloperActivity")

        A: android:exported(0x01010010)=(type 0x12)0x0

      E: provider (line=53)

        A: android:name(0x01010003)="androidx.startup.InitializationProvider" (Raw: "androidx.startup.InitializationProvider")

        A: android:exported(0x01010010)=(type 0x12)0x0

        A: android:authorities(0x01010018)="com.c2hapmll.tools.androidx-startup" (Raw: "com.c2hapmll.tools.androidx-startup")

        E: meta-data (line=57)

          A: android:name(0x01010003)="androidx.emoji2.text.EmojiCompatInitializer" (Raw: "androidx.emoji2.text.EmojiCompatInitializer")

          A: android:value(0x01010024)="androidx.startup" (Raw: "androidx.startup")

        E: meta-data (line=60)

          A: android:name(0x01010003)="androidx.lifecycle.ProcessLifecycleInitializer" (Raw: "androidx.lifecycle.ProcessLifecycleInitializer")

          A: android:value(0x01010024)="androidx.startup" (Raw: "androidx.startup")

        E: meta-data (line=63)

          A: android:name(0x01010003)="androidx.profileinstaller.ProfileInstallerInitializer" (Raw: "androidx.profileinstaller.ProfileInstallerInitializer")

          A: android:value(0x01010024)="androidx.startup" (Raw: "androidx.startup")

      E: receiver (line=68)

        A: android:name(0x01010003)="androidx.profileinstaller.ProfileInstallReceiver" (Raw: "androidx.profileinstaller.ProfileInstallReceiver")

        A: android:permission(0x01010006)="android.permission.DUMP" (Raw: "android.permission.DUMP")

        A: android:enabled(0x0101000e)=(type 0x12)0xffffffff

        A: android:exported(0x01010010)=(type 0x12)0xffffffff

        A: android:directBootAware(0x01010505)=(type 0x12)0x0

        E: intent-filter (line=74)

          E: action (line=75)

            A: android:name(0x01010003)="androidx.profileinstaller.action.INSTALL_PROFILE" (Raw: "androidx.profileinstaller.action.INSTALL_PROFILE")

        E: intent-filter (line=77)

          E: action (line=78)

            A: android:name(0x01010003)="androidx.profileinstaller.action.SKIP_FILE" (Raw: "androidx.profileinstaller.action.SKIP_FILE")

        E: intent-filter (line=80)

          E: action (line=81)

            A: android:name(0x01010003)="androidx.profileinstaller.action.SAVE_PROFILE" (Raw: "androidx.profileinstaller.action.SAVE_PROFILE")

        E: intent-filter (line=83)

          E: action (line=84)

            A: android:name(0x01010003)="androidx.profileinstaller.action.BENCHMARK_OPERATION" (Raw: "androidx.profileinstaller.action.BENCHMARK_OPERATION")


07/30/2025 15:13:51 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:51 [DEBUG] [filename:main.py] [line:348] set old_application_name : 
07/30/2025 15:13:51 [DEBUG] [filename:main.py] [line:568] no anti_activity_hijack
07/30/2025 15:13:51 [DEBUG] [filename:main.py] [line:605] Dialog antideug succeed!
07/30/2025 15:13:51 [DEBUG] [filename:execute_cmd.py] [line:49] output file path --> D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\decompiler\AndroidManifestout.xml

Start to process manifest file 


07/30/2025 15:13:51 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:51 [DEBUG] [filename:main.py] [line:911] update extractNativeLibs successed!
07/30/2025 15:13:51 [DEBUG] [filename:aapt.py] [line:252] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\tools\apktool\aapt.exe dump xmltree D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk AndroidManifest.xml
07/30/2025 15:13:51 [DEBUG] [filename:execute_cmd.py] [line:49] N: android=http://schemas.android.com/apk/res/android

  E: manifest (line=2)

    A: android:versionCode(0x0101021b)=(type 0x10)0x1

    A: android:versionName(0x0101021c)="1.0" (Raw: "1.0")

    A: android:compileSdkVersion(0x01010572)=(type 0x10)0x22

    A: android:compileSdkVersionCodename(0x01010573)="14" (Raw: "14")

    A: package="com.c2hapmll.tools" (Raw: "com.c2hapmll.tools")

    A: platformBuildVersionCode=(type 0x10)0x22

    A: platformBuildVersionName=(type 0x10)0xe

    E: uses-sdk (line=7)

      A: android:minSdkVersion(0x0101020c)=(type 0x10)0x1a

      A: android:targetSdkVersion(0x01010270)=(type 0x10)0x22

    E: uses-permission (line=12)

      A: android:name(0x01010003)="android.permission.BLUETOOTH" (Raw: "android.permission.BLUETOOTH")

    E: uses-permission (line=13)

      A: android:name(0x01010003)="android.permission.BLUETOOTH_ADMIN" (Raw: "android.permission.BLUETOOTH_ADMIN")

    E: uses-permission (line=14)

      A: android:name(0x01010003)="android.permission.ACCESS_FINE_LOCATION" (Raw: "android.permission.ACCESS_FINE_LOCATION")

    E: uses-permission (line=15)

      A: android:name(0x01010003)="android.permission.BLUETOOTH_SCAN" (Raw: "android.permission.BLUETOOTH_SCAN")

    E: uses-permission (line=16)

      A: android:name(0x01010003)="android.permission.BLUETOOTH_CONNECT" (Raw: "android.permission.BLUETOOTH_CONNECT")

    E: uses-permission (line=17)

      A: android:name(0x01010003)="android.permission.BLUETOOTH_ADVERTISE" (Raw: "android.permission.BLUETOOTH_ADVERTISE")

    E: uses-feature (line=19)

      A: android:name(0x01010003)="android.hardware.bluetooth" (Raw: "android.hardware.bluetooth")

      A: android:required(0x0101028e)=(type 0x12)0xffffffff

    E: permission (line=23)

      A: android:name(0x01010003)="com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" (Raw: "com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION")

      A: android:protectionLevel(0x01010009)=(type 0x11)0x2

    E: uses-permission (line=27)

      A: android:name(0x01010003)="com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" (Raw: "com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION")

    E: application (line=29)

      A: android:theme(0x01010000)=@0x7f100272

      A: android:label(0x01010001)=@0x7f0f001c

      A: android:icon(0x01010002)=@0x7f0d0000

      A: android:allowBackup(0x01010280)=(type 0x12)0xffffffff

      A: android:supportsRtl(0x010103af)=(type 0x12)0xffffffff

      A: android:extractNativeLibs(0x010104ea)=(type 0x12)0x0

      A: android:fullBackupContent(0x010104eb)=@0x7f120000

      A: android:roundIcon(0x0101052c)=@0x7f0d0002

      A: android:appComponentFactory(0x0101057a)="androidx.core.app.CoreComponentFactory" (Raw: "androidx.core.app.CoreComponentFactory")

      A: android:dataExtractionRules(0x0101063e)=@0x7f120001

      E: activity (line=40)

        A: android:name(0x01010003)="com.c2hapmll.tools.MainActivity" (Raw: "com.c2hapmll.tools.MainActivity")

        A: android:exported(0x01010010)=(type 0x12)0xffffffff

        E: intent-filter (line=43)

          E: action (line=44)

            A: android:name(0x01010003)="android.intent.action.MAIN" (Raw: "android.intent.action.MAIN")

          E: category (line=46)

            A: android:name(0x01010003)="android.intent.category.LAUNCHER" (Raw: "android.intent.category.LAUNCHER")

      E: activity (line=49)

        A: android:name(0x01010003)="com.c2hapmll.tools.DeveloperActivity" (Raw: "com.c2hapmll.tools.DeveloperActivity")

        A: android:exported(0x01010010)=(type 0x12)0x0

      E: provider (line=53)

        A: android:name(0x01010003)="androidx.startup.InitializationProvider" (Raw: "androidx.startup.InitializationProvider")

        A: android:exported(0x01010010)=(type 0x12)0x0

        A: android:authorities(0x01010018)="com.c2hapmll.tools.androidx-startup" (Raw: "com.c2hapmll.tools.androidx-startup")

        E: meta-data (line=57)

          A: android:name(0x01010003)="androidx.emoji2.text.EmojiCompatInitializer" (Raw: "androidx.emoji2.text.EmojiCompatInitializer")

          A: android:value(0x01010024)="androidx.startup" (Raw: "androidx.startup")

        E: meta-data (line=60)

          A: android:name(0x01010003)="androidx.lifecycle.ProcessLifecycleInitializer" (Raw: "androidx.lifecycle.ProcessLifecycleInitializer")

          A: android:value(0x01010024)="androidx.startup" (Raw: "androidx.startup")

        E: meta-data (line=63)

          A: android:name(0x01010003)="androidx.profileinstaller.ProfileInstallerInitializer" (Raw: "androidx.profileinstaller.ProfileInstallerInitializer")

          A: android:value(0x01010024)="androidx.startup" (Raw: "androidx.startup")

      E: receiver (line=68)

        A: android:name(0x01010003)="androidx.profileinstaller.ProfileInstallReceiver" (Raw: "androidx.profileinstaller.ProfileInstallReceiver")

        A: android:permission(0x01010006)="android.permission.DUMP" (Raw: "android.permission.DUMP")

        A: android:enabled(0x0101000e)=(type 0x12)0xffffffff

        A: android:exported(0x01010010)=(type 0x12)0xffffffff

        A: android:directBootAware(0x01010505)=(type 0x12)0x0

        E: intent-filter (line=74)

          E: action (line=75)

            A: android:name(0x01010003)="androidx.profileinstaller.action.INSTALL_PROFILE" (Raw: "androidx.profileinstaller.action.INSTALL_PROFILE")

        E: intent-filter (line=77)

          E: action (line=78)

            A: android:name(0x01010003)="androidx.profileinstaller.action.SKIP_FILE" (Raw: "androidx.profileinstaller.action.SKIP_FILE")

        E: intent-filter (line=80)

          E: action (line=81)

            A: android:name(0x01010003)="androidx.profileinstaller.action.SAVE_PROFILE" (Raw: "androidx.profileinstaller.action.SAVE_PROFILE")

        E: intent-filter (line=83)

          E: action (line=84)

            A: android:name(0x01010003)="androidx.profileinstaller.action.BENCHMARK_OPERATION" (Raw: "androidx.profileinstaller.action.BENCHMARK_OPERATION")


07/30/2025 15:13:51 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:51 [DEBUG] [filename:handle_manifest_apeditor.py] [line:32] ApEditor change application name success!!
07/30/2025 15:13:51 [DEBUG] [filename:handle_manifest_apeditor.py] [line:48] wirte android.app.Application into /virtual
07/30/2025 15:13:51 [DEBUG] [filename:apk.py] [line:17] zip -d D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk AndroidManifest.xml
07/30/2025 15:13:51 [DEBUG] [filename:execute_cmd.py] [line:49] deleting: AndroidManifest.xml

07/30/2025 15:13:51 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:51 [DEBUG] [filename:apk.py] [line:21] delete the file AndroidManifest.xml successed!
07/30/2025 15:13:51 [DEBUG] [filename:aapt.py] [line:252] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\tools\apktool\aapt.exe dump xmltree D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk AndroidManifest.xml
07/30/2025 15:13:51 [DEBUG] [filename:execute_cmd.py] [line:49] N: android=http://schemas.android.com/apk/res/android

  E: manifest (line=2)

    A: package="com.c2hapmll.tools" (Raw: "com.c2hapmll.tools")

    A: platformBuildVersionCode=(type 0x10)0x22

    A: platformBuildVersionName=(type 0x10)0xe

    A: android:versionCode(0x0101021b)=(type 0x10)0x1

    A: android:versionName(0x0101021c)="1.0" (Raw: "1.0")

    A: android:compileSdkVersion(0x01010572)=(type 0x10)0x22

    A: android:compileSdkVersionCodename(0x01010573)="14" (Raw: "14")

    E: uses-sdk (line=7)

      A: android:minSdkVersion(0x0101020c)=(type 0x10)0x1a

      A: android:targetSdkVersion(0x01010270)=(type 0x10)0x22

    E: uses-permission (line=12)

      A: android:name(0x01010003)="android.permission.BLUETOOTH" (Raw: "android.permission.BLUETOOTH")

    E: uses-permission (line=13)

      A: android:name(0x01010003)="android.permission.BLUETOOTH_ADMIN" (Raw: "android.permission.BLUETOOTH_ADMIN")

    E: uses-permission (line=14)

      A: android:name(0x01010003)="android.permission.ACCESS_FINE_LOCATION" (Raw: "android.permission.ACCESS_FINE_LOCATION")

    E: uses-permission (line=15)

      A: android:name(0x01010003)="android.permission.BLUETOOTH_SCAN" (Raw: "android.permission.BLUETOOTH_SCAN")

    E: uses-permission (line=16)

      A: android:name(0x01010003)="android.permission.BLUETOOTH_CONNECT" (Raw: "android.permission.BLUETOOTH_CONNECT")

    E: uses-permission (line=17)

      A: android:name(0x01010003)="android.permission.BLUETOOTH_ADVERTISE" (Raw: "android.permission.BLUETOOTH_ADVERTISE")

    E: uses-feature (line=19)

      A: android:name(0x01010003)="android.hardware.bluetooth" (Raw: "android.hardware.bluetooth")

      A: android:required(0x0101028e)=(type 0x12)0xffffffff

    E: permission (line=23)

      A: android:name(0x01010003)="com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" (Raw: "com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION")

      A: android:protectionLevel(0x01010009)=(type 0x11)0x2

    E: uses-permission (line=27)

      A: android:name(0x01010003)="com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" (Raw: "com.c2hapmll.tools.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION")

    E: application (line=29)

      A: android:theme(0x01010000)=@0x7f100272

      A: android:label(0x01010001)=@0x7f0f001c

      A: android:icon(0x01010002)=@0x7f0d0000

      A: android:name(0x01010003)="com.payegis.FirstApplication" (Raw: "com.payegis.FirstApplication")

      A: android:allowBackup(0x01010280)=(type 0x12)0xffffffff

      A: android:supportsRtl(0x010103af)=(type 0x12)0xffffffff

      A: android:extractNativeLibs(0x010104ea)=(type 0x12)0xffffffff

      A: android:fullBackupContent(0x010104eb)=@0x7f120000

      A: android:roundIcon(0x0101052c)=@0x7f0d0002

      A: android:appComponentFactory(0x0101057a)="androidx.core.app.CoreComponentFactory" (Raw: "androidx.core.app.CoreComponentFactory")

      A: android:dataExtractionRules(0x0101063e)=@0x7f120001

      E: activity (line=40)

        A: android:name(0x01010003)="com.c2hapmll.tools.MainActivity" (Raw: "com.c2hapmll.tools.MainActivity")

        A: android:exported(0x01010010)=(type 0x12)0xffffffff

        E: intent-filter (line=43)

          E: action (line=44)

            A: android:name(0x01010003)="android.intent.action.MAIN" (Raw: "android.intent.action.MAIN")

          E: category (line=46)

            A: android:name(0x01010003)="android.intent.category.LAUNCHER" (Raw: "android.intent.category.LAUNCHER")

      E: activity (line=49)

        A: android:name(0x01010003)="com.c2hapmll.tools.DeveloperActivity" (Raw: "com.c2hapmll.tools.DeveloperActivity")

        A: android:exported(0x01010010)=(type 0x12)0x0

      E: provider (line=53)

        A: android:name(0x01010003)="androidx.startup.InitializationProvider" (Raw: "androidx.startup.InitializationProvider")

        A: android:exported(0x01010010)=(type 0x12)0x0

        A: android:authorities(0x01010018)="com.c2hapmll.tools.androidx-startup" (Raw: "com.c2hapmll.tools.androidx-startup")

        E: meta-data (line=57)

          A: android:name(0x01010003)="androidx.emoji2.text.EmojiCompatInitializer" (Raw: "androidx.emoji2.text.EmojiCompatInitializer")

          A: android:value(0x01010024)="androidx.startup" (Raw: "androidx.startup")

        E: meta-data (line=60)

          A: android:name(0x01010003)="androidx.lifecycle.ProcessLifecycleInitializer" (Raw: "androidx.lifecycle.ProcessLifecycleInitializer")

          A: android:value(0x01010024)="androidx.startup" (Raw: "androidx.startup")

        E: meta-data (line=63)

          A: android:name(0x01010003)="androidx.profileinstaller.ProfileInstallerInitializer" (Raw: "androidx.profileinstaller.ProfileInstallerInitializer")

          A: android:value(0x01010024)="androidx.startup" (Raw: "androidx.startup")

      E: receiver (line=68)

        A: android:name(0x01010003)="androidx.profileinstaller.ProfileInstallReceiver" (Raw: "androidx.profileinstaller.ProfileInstallReceiver")

        A: android:permission(0x01010006)="android.permission.DUMP" (Raw: "android.permission.DUMP")

        A: android:enabled(0x0101000e)=(type 0x12)0xffffffff

        A: android:exported(0x01010010)=(type 0x12)0xffffffff

        A: android:directBootAware(0x01010505)=(type 0x12)0x0

        E: intent-filter (line=74)

          E: action (line=75)

            A: android:name(0x01010003)="androidx.profileinstaller.action.INSTALL_PROFILE" (Raw: "androidx.profileinstaller.action.INSTALL_PROFILE")

        E: intent-filter (line=77)

          E: action (line=78)

            A: android:name(0x01010003)="androidx.profileinstaller.action.SKIP_FILE" (Raw: "androidx.profileinstaller.action.SKIP_FILE")

        E: intent-filter (line=80)

          E: action (line=81)

            A: android:name(0x01010003)="androidx.profileinstaller.action.SAVE_PROFILE" (Raw: "androidx.profileinstaller.action.SAVE_PROFILE")

        E: intent-filter (line=83)

          E: action (line=84)

            A: android:name(0x01010003)="androidx.profileinstaller.action.BENCHMARK_OPERATION" (Raw: "androidx.profileinstaller.action.BENCHMARK_OPERATION")


07/30/2025 15:13:51 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:51 [DEBUG] [filename:aapt.py] [line:207] the application name is com.payegis.FirstApplication
07/30/2025 15:13:51 [INFO] [filename:main.py] [line:936] modify the AndroidManifest.xml successed!
07/30/2025 15:13:51 [DEBUG] [filename:main.py] [line:950] no js_obfuscator
07/30/2025 15:13:52 [DEBUG] [filename:smali.py] [line:48] java -jar  D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\tools\smali\smali-2.2.5.jar a D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\decompiler\smali2 -o D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\decompiler\new\classes2.dex
07/30/2025 15:13:52 [DEBUG] [filename:execute_cmd.py] [line:49] 
07/30/2025 15:13:52 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:53 [DEBUG] [filename:apk.py] [line:17] zip -d D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk classes.dex
07/30/2025 15:13:53 [DEBUG] [filename:execute_cmd.py] [line:49] deleting: classes.dex

07/30/2025 15:13:53 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:53 [DEBUG] [filename:apk.py] [line:21] delete the file classes.dex successed!
07/30/2025 15:13:53 [DEBUG] [filename:apk.py] [line:17] zip -d D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk classes2.dex
07/30/2025 15:13:53 [DEBUG] [filename:execute_cmd.py] [line:49] 	zip warning: name not matched: classes2.dex

zip error: Nothing to do! (D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk)

07/30/2025 15:13:53 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:53 [ERROR] [filename:apk.py] [line:25] delete the file classes2.dex failed!
07/30/2025 15:13:53 [DEBUG] [filename:execute_cmd.py] [line:49] 
07/30/2025 15:13:53 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:53 [DEBUG] [filename:main.py] [line:1030] Create directory D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\dexfolder success
07/30/2025 15:13:53 [DEBUG] [filename:apk.py] [line:116] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk
07/30/2025 15:13:53 [DEBUG] [filename:apk.py] [line:117] .dex
07/30/2025 15:13:53 [DEBUG] [filename:apk.py] [line:121] classes.dex
07/30/2025 15:13:53 [DEBUG] [filename:apk.py] [line:121] classes2.dex
07/30/2025 15:13:54 [DEBUG] [filename:count_dex_file.py] [line:45] 2
07/30/2025 15:13:54 [DEBUG] [filename:execute_cmd.py] [line:49] 
07/30/2025 15:13:54 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:54 [DEBUG] [filename:combine_dex.py] [line:10] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\CombineDex.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\compress.zip D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\shell.dex  D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\protected.dex
07/30/2025 15:13:54 [DEBUG] [filename:execute_cmd.py] [line:49] older dex file size 3576 
new dex file  size 3228150 
older sha1_digest: e0 70 97 1d a5 6b 02 34 97 e7 2c b6 22 ff dc 61 04 c9 f1 02 
new sha1_digest: d5 19 37 9c 0d d0 f7 b7 55 a9 c1 d5 08 d1 be 58 5a d8 41 17 
older checksum : e0 5a b4 e7 
new  checksum : c8 ed 13 ba 

07/30/2025 15:13:54 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:54 [DEBUG] [filename:combine_dex.py] [line:13] combine dex successed!
07/30/2025 15:13:54 [INFO] [filename:main.py] [line:1245] combine the compress.zip shell.dex successed!
07/30/2025 15:13:54 [DEBUG] [filename:custom_param.py] [line:158] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\CustomParamSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi/libegis.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi/libegis.so 0 None None
07/30/2025 15:13:55 [DEBUG] [filename:execute_cmd.py] [line:49] File  D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi/libegis.so size is 105b80
this is 32 bit library
store the g_custom_flag successed!

07/30/2025 15:13:55 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:55 [DEBUG] [filename:custom_param.py] [line:165] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\CustomParamSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi-v7a/libegis.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi-v7a/libegis.so 0 None None
07/30/2025 15:13:55 [DEBUG] [filename:execute_cmd.py] [line:49] File  D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi-v7a/libegis.so size is efb84
this is 32 bit library
store the g_custom_flag successed!

07/30/2025 15:13:55 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:55 [DEBUG] [filename:custom_param.py] [line:172] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\CustomParamSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/arm64-v8a/libegis.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/arm64-v8a/libegis.so 0 None None
07/30/2025 15:13:55 [DEBUG] [filename:execute_cmd.py] [line:49] File  D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/arm64-v8a/libegis.so size is 16cea8
this is 64 bit library
store the g_custom_flag successed!

07/30/2025 15:13:55 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:55 [DEBUG] [filename:custom_param.py] [line:179] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\CustomParamSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86/libegis-x86.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86/libegis-x86.so 0 None None
07/30/2025 15:13:55 [DEBUG] [filename:execute_cmd.py] [line:49] File  D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86/libegis-x86.so size is 178d9c
this is 32 bit library
store the g_custom_flag successed!

07/30/2025 15:13:55 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:55 [DEBUG] [filename:custom_param.py] [line:186] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\CustomParamSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86_64/libegis-x86.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86_64/libegis-x86.so 0 None None
07/30/2025 15:13:55 [DEBUG] [filename:execute_cmd.py] [line:49] File  D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86_64/libegis-x86.so size is 181208
this is 64 bit library
store the g_custom_flag successed!

07/30/2025 15:13:55 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:55 [DEBUG] [filename:custom_param.py] [line:158] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\CustomParamSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi/libegis.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi/libegis.so 32768 None new
07/30/2025 15:13:55 [DEBUG] [filename:execute_cmd.py] [line:49] File  D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi/libegis.so size is 105b80
this is 32 bit library
store the g_custom_flag successed!

07/30/2025 15:13:55 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:55 [DEBUG] [filename:custom_param.py] [line:165] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\CustomParamSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi-v7a/libegis.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi-v7a/libegis.so 32768 None new
07/30/2025 15:13:56 [DEBUG] [filename:execute_cmd.py] [line:49] File  D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi-v7a/libegis.so size is efb84
this is 32 bit library
store the g_custom_flag successed!

07/30/2025 15:13:56 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:56 [DEBUG] [filename:custom_param.py] [line:172] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\CustomParamSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/arm64-v8a/libegis.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/arm64-v8a/libegis.so 32768 None new
07/30/2025 15:13:56 [DEBUG] [filename:execute_cmd.py] [line:49] File  D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/arm64-v8a/libegis.so size is 16cea8
this is 64 bit library
store the g_custom_flag successed!

07/30/2025 15:13:56 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:56 [DEBUG] [filename:custom_param.py] [line:179] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\CustomParamSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86/libegis-x86.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86/libegis-x86.so 32768 None new
07/30/2025 15:13:56 [DEBUG] [filename:execute_cmd.py] [line:49] File  D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86/libegis-x86.so size is 178d9c
this is 32 bit library
store the g_custom_flag successed!

07/30/2025 15:13:56 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:56 [DEBUG] [filename:custom_param.py] [line:186] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\CustomParamSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86_64/libegis-x86.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86_64/libegis-x86.so 32768 None new
07/30/2025 15:13:56 [DEBUG] [filename:execute_cmd.py] [line:49] File  D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86_64/libegis-x86.so size is 181208
this is 64 bit library
store the g_custom_flag successed!

07/30/2025 15:13:56 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:56 [INFO] [filename:main.py] [line:1285] set customization paramers into libegis.so successed!
07/30/2025 15:13:56 [DEBUG] [filename:protect_so.py] [line:27] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\protectSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi/libegis.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi/libegis.so 
07/30/2025 15:13:56 [DEBUG] [filename:execute_cmd.py] [line:49] File size is 1072000
this is 32 bit library
Ident : ELF
Type  : 3
Machine : 40
Version : 1
Entry point address: 0x00000000
Start of program headers: 52
Start of section headers: 1070960
Flags : 0x05000200
Size of this header: : 52
Size of program headers: 32
Number of program headers: : 8
Size of section headers: : 40
Number of section headers: 26
Section header string table index: 25

------>Dynamic [0] :0x00000003
------>Dynamic [1] :0x00000002
------>Dynamic [2] :0x00000017
------>Dynamic [3] :0x00000014
------>Dynamic [4] :0x00000011
------>Dynamic [5] :0x00000012
------>Dynamic [6] :0x00000013
------>Dynamic [7] :0x6ffffffa
------>Dynamic [8] :0x00000006
------>Dynamic [9] :0x0000000b
------>Dynamic [10] :0x00000005
--------->DT_STRTAB : 0x6fffffef8e98
------>Dynamic [11] :0x0000000a
------>Dynamic [12] :0x00000004
------>Dynamic [13] :0x00000001
------>Dynamic [14] :0x00000001
------>Dynamic [15] :0x00000001
------>Dynamic [16] :0x00000001
------>Dynamic [17] :0x00000001
------>Dynamic [18] :0x00000001
------>Dynamic [19] :0x0000000e
------>Dynamic [20] :0x0000001a
------>Dynamic [21] :0x0000001c
------>Dynamic [22] :0x00000019
------>Dynamic [23] :0x0000001b
------>Dynamic [24] :0x0000001e
------>Dynamic [25] :0x6ffffffb
------>Dynamic [26] :0x6ffffff0
------>Dynamic [27] :0x6ffffffc
------>Dynamic [28] :0x6ffffffd
------>Dynamic [29] :0x6ffffffe
------>Dynamic [30] :0x6fffffff
------>Dynamic [31] :0x00000000
------>Dynamic [32] :0x00000000
------>Dynamic [33] :0x00000000
------>Dynamic [34] :0x00000000
------>Dynamic [35] :0x00000000
------>Dynamic [36] :0x00000000

Section[0] : 0x00000000
---->sh_name :
---->sh_flags :0x00000000
---->sh_offset :0
---->sh_size :0
---->sh_link :0
---->sh_info :0
---->sh_addralign :0
---->sh_entsize :0

Section[1] : 0x00000007
---->sh_name :.note.gnu.build-id
---->sh_flags :0x00000002
---->sh_offset :308
---->sh_size :36
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[2] : 0x0000000b
---->sh_name :.dynsym
---->sh_flags :0x00000002
---->sh_offset :344
---->sh_size :36144
---->sh_link :3
---->sh_info :1
---->sh_addralign :4
---->sh_entsize :16

------>All Symbols[012] : JNI_OnLoad                               | 34261     (offset) | 234       (code) | 11        (index) 

sym_index : 1
------>All Symbols[209] : tl_mmap                                  | 828f0     (offset) | 28        (code) | 11        (index) 

sym_index : 2
Section[3] : 0x00000003
---->sh_name :.dynstr
---->sh_flags :0x00000002
---->sh_offset :36488
---->sh_size :101410
---->sh_link :0
---->sh_info :0
---->sh_addralign :1
---->sh_entsize :0

Section[4] : 0x00000005
---->sh_name :.hash
---->sh_flags :0x00000002
---->sh_offset :137900
---->sh_size :17256
---->sh_link :2
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :4

Section[5] : 0x6fffffff
---->sh_name :.gnu.version
---->sh_flags :0x00000002
---->sh_offset :155156
---->sh_size :4518
---->sh_link :2
---->sh_info :0
---->sh_addralign :2
---->sh_entsize :2

Section[6] : 0x6ffffffd
---->sh_name :.gnu.version_d
---->sh_flags :0x00000002
---->sh_offset :159676
---->sh_size :28
---->sh_link :3
---->sh_info :1
---->sh_addralign :4
---->sh_entsize :0

Section[7] : 0x6ffffffe
---->sh_name :.gnu.version_r
---->sh_flags :0x00000002
---->sh_offset :159704
---->sh_size :96
---->sh_link :3
---->sh_info :3
---->sh_addralign :4
---->sh_entsize :0

Section[8] : 0x00000009
---->sh_name :.rel.dyn
---->sh_flags :0x00000002
---->sh_offset :159800
---->sh_size :24776
---->sh_link :2
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :8

Section[9] : 0x00000009
---->sh_name :.rel.plt
---->sh_flags :0x00000042
---->sh_offset :184576
---->sh_size :8152
---->sh_link :2
---->sh_info :10
---->sh_addralign :4
---->sh_entsize :8

Section[10] : 0x00000001
---->sh_name :.plt
---->sh_flags :0x00000006
---->sh_offset :192728
---->sh_size :12248
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[11] : 0x00000001
---->sh_name :.text
---->sh_flags :0x00000006
---->sh_offset :204976
---->sh_size :685208
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[12] : 0x00000001
---->sh_name :.ARM.extab
---->sh_flags :0x00000002
---->sh_offset :890184
---->sh_size :53288
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[13] : 0x70000001
---->sh_name :.ARM.exidx
---->sh_flags :0x00000082
---->sh_offset :943472
---->sh_size :21144
---->sh_link :11
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :8

Section[14] : 0x00000001
---->sh_name :.rodata
---->sh_flags :0x00000002
---->sh_offset :964624
---->sh_size :79538
---->sh_link :0
---->sh_info :0
---->sh_addralign :16
---->sh_entsize :0

Section[15] : 0x0000000f
---->sh_name :.fini_array
---->sh_flags :0x00000003
---->sh_offset :1046032
---->sh_size :8
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[16] : 0x00000001
---->sh_name :.data.rel.ro
---->sh_flags :0x00000003
---->sh_offset :1046048
---->sh_size :13680
---->sh_link :0
---->sh_info :0
---->sh_addralign :16
---->sh_entsize :0

Section[17] : 0x0000000e
---->sh_name :.init_array
---->sh_flags :0x00000003
---->sh_offset :1059728
---->sh_size :44
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[18] : 0x00000006
---->sh_name :.dynamic
---->sh_flags :0x00000003
---->sh_offset :1059772
---->sh_size :296
---->sh_link :3
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :8

Section[19] : 0x00000001
---->sh_name :.got
---->sh_flags :0x00000003
---->sh_offset :1060072
---->sh_size :4888
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[20] : 0x00000001
---->sh_name :.data
---->sh_flags :0x00000003
---->sh_offset :1064960
---->sh_size :5556
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[21] : 0x00000008
---->sh_name :.bss
---->sh_flags :0x00000003
---->sh_offset :1070516
---->sh_size :4224
---->sh_link :0
---->sh_info :0
---->sh_addralign :16
---->sh_entsize :0

Section[22] : 0x00000001
---->sh_name :.comment
---->sh_flags :0x00000030
---->sh_offset :1070516
---->sh_size :101
---->sh_link :0
---->sh_info :0
---->sh_addralign :1
---->sh_entsize :1

Section[23] : 0x00000007
---->sh_name :.note.gnu.gold-version
---->sh_flags :0x00000000
---->sh_offset :1070620
---->sh_size :28
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[24] : 0x70000003
---->sh_name :.ARM.attributes
---->sh_flags :0x00000000
---->sh_offset :1070648
---->sh_size :52
---->sh_link :0
---->sh_info :0
---->sh_addralign :1
---->sh_entsize :0

Section[25] : 0x00000003
---->sh_name :.shstrtab
---->sh_flags :0x00000000
---->sh_offset :1070700
---->sh_size :259
---->sh_link :0
---->sh_info :0
---->sh_addralign :1
---->sh_entsize :0


07/30/2025 15:13:56 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:56 [DEBUG] [filename:protect_so.py] [line:35] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\protectSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi-v7a/libegis.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi-v7a/libegis.so 
07/30/2025 15:13:56 [DEBUG] [filename:execute_cmd.py] [line:49] File size is 981892
this is 32 bit library
Ident : ELF
Type  : 3
Machine : 40
Version : 1
Entry point address: 0x00000000
Start of program headers: 52
Start of section headers: 980852
Flags : 0x05000200
Size of this header: : 52
Size of program headers: 32
Number of program headers: : 8
Size of section headers: : 40
Number of section headers: 26
Section header string table index: 25

------>Dynamic [0] :0x00000003
------>Dynamic [1] :0x00000002
------>Dynamic [2] :0x00000017
------>Dynamic [3] :0x00000014
------>Dynamic [4] :0x00000011
------>Dynamic [5] :0x00000012
------>Dynamic [6] :0x00000013
------>Dynamic [7] :0x6ffffffa
------>Dynamic [8] :0x00000006
------>Dynamic [9] :0x0000000b
------>Dynamic [10] :0x00000005
--------->DT_STRTAB : 0x6ffffff18d38
------>Dynamic [11] :0x0000000a
------>Dynamic [12] :0x00000004
------>Dynamic [13] :0x00000001
------>Dynamic [14] :0x00000001
------>Dynamic [15] :0x00000001
------>Dynamic [16] :0x00000001
------>Dynamic [17] :0x00000001
------>Dynamic [18] :0x00000001
------>Dynamic [19] :0x0000000e
------>Dynamic [20] :0x0000001a
------>Dynamic [21] :0x0000001c
------>Dynamic [22] :0x00000019
------>Dynamic [23] :0x0000001b
------>Dynamic [24] :0x0000001e
------>Dynamic [25] :0x6ffffffb
------>Dynamic [26] :0x6ffffff0
------>Dynamic [27] :0x6ffffffc
------>Dynamic [28] :0x6ffffffd
------>Dynamic [29] :0x6ffffffe
------>Dynamic [30] :0x6fffffff
------>Dynamic [31] :0x00000000
------>Dynamic [32] :0x00000000
------>Dynamic [33] :0x00000000
------>Dynamic [34] :0x00000000
------>Dynamic [35] :0x00000000
------>Dynamic [36] :0x00000000

Section[0] : 0x00000000
---->sh_name :
---->sh_flags :0x00000000
---->sh_offset :0
---->sh_size :0
---->sh_link :0
---->sh_info :0
---->sh_addralign :0
---->sh_entsize :0

Section[1] : 0x00000007
---->sh_name :.note.gnu.build-id
---->sh_flags :0x00000002
---->sh_offset :308
---->sh_size :36
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[2] : 0x0000000b
---->sh_name :.dynsym
---->sh_flags :0x00000002
---->sh_offset :344
---->sh_size :35792
---->sh_link :3
---->sh_info :1
---->sh_addralign :4
---->sh_entsize :16

------>All Symbols[012] : JNI_OnLoad                               | 33afd     (offset) | 230       (code) | 11        (index) 

sym_index : 1
------>All Symbols[184] : tl_mmap                                  | 7999c     (offset) | 28        (code) | 11        (index) 

sym_index : 2
Section[3] : 0x00000003
---->sh_name :.dynstr
---->sh_flags :0x00000002
---->sh_offset :36136
---->sh_size :101098
---->sh_link :0
---->sh_info :0
---->sh_addralign :1
---->sh_entsize :0

Section[4] : 0x00000005
---->sh_name :.hash
---->sh_flags :0x00000002
---->sh_offset :137236
---->sh_size :17168
---->sh_link :2
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :4

Section[5] : 0x6fffffff
---->sh_name :.gnu.version
---->sh_flags :0x00000002
---->sh_offset :154404
---->sh_size :4474
---->sh_link :2
---->sh_info :0
---->sh_addralign :2
---->sh_entsize :2

Section[6] : 0x6ffffffd
---->sh_name :.gnu.version_d
---->sh_flags :0x00000002
---->sh_offset :158880
---->sh_size :28
---->sh_link :3
---->sh_info :1
---->sh_addralign :4
---->sh_entsize :0

Section[7] : 0x6ffffffe
---->sh_name :.gnu.version_r
---->sh_flags :0x00000002
---->sh_offset :158908
---->sh_size :96
---->sh_link :3
---->sh_info :3
---->sh_addralign :4
---->sh_entsize :0

Section[8] : 0x00000009
---->sh_name :.rel.dyn
---->sh_flags :0x00000002
---->sh_offset :159004
---->sh_size :24776
---->sh_link :2
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :8

Section[9] : 0x00000009
---->sh_name :.rel.plt
---->sh_flags :0x00000042
---->sh_offset :183780
---->sh_size :7944
---->sh_link :2
---->sh_info :10
---->sh_addralign :4
---->sh_entsize :8

Section[10] : 0x00000001
---->sh_name :.plt
---->sh_flags :0x00000006
---->sh_offset :191724
---->sh_size :11936
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[11] : 0x00000001
---->sh_name :.text
---->sh_flags :0x00000006
---->sh_offset :203664
---->sh_size :593448
---->sh_link :0
---->sh_info :0
---->sh_addralign :8
---->sh_entsize :0

Section[12] : 0x00000001
---->sh_name :.ARM.extab
---->sh_flags :0x00000002
---->sh_offset :797112
---->sh_size :53136
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[13] : 0x70000001
---->sh_name :.ARM.exidx
---->sh_flags :0x00000082
---->sh_offset :850248
---->sh_size :23256
---->sh_link :11
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :8

Section[14] : 0x00000001
---->sh_name :.rodata
---->sh_flags :0x00000002
---->sh_offset :873504
---->sh_size :79794
---->sh_link :0
---->sh_info :0
---->sh_addralign :16
---->sh_entsize :0

Section[15] : 0x0000000f
---->sh_name :.fini_array
---->sh_flags :0x00000003
---->sh_offset :956016
---->sh_size :8
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[16] : 0x00000001
---->sh_name :.data.rel.ro
---->sh_flags :0x00000003
---->sh_offset :956032
---->sh_size :13680
---->sh_link :0
---->sh_info :0
---->sh_addralign :16
---->sh_entsize :0

Section[17] : 0x0000000e
---->sh_name :.init_array
---->sh_flags :0x00000003
---->sh_offset :969712
---->sh_size :44
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[18] : 0x00000006
---->sh_name :.dynamic
---->sh_flags :0x00000003
---->sh_offset :969756
---->sh_size :296
---->sh_link :3
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :8

Section[19] : 0x00000001
---->sh_name :.got
---->sh_flags :0x00000003
---->sh_offset :970064
---->sh_size :4784
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[20] : 0x00000001
---->sh_name :.data
---->sh_flags :0x00000003
---->sh_offset :974848
---->sh_size :5556
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[21] : 0x00000008
---->sh_name :.bss
---->sh_flags :0x00000003
---->sh_offset :980404
---->sh_size :4288
---->sh_link :0
---->sh_info :0
---->sh_addralign :16
---->sh_entsize :0

Section[22] : 0x00000001
---->sh_name :.comment
---->sh_flags :0x00000030
---->sh_offset :980404
---->sh_size :101
---->sh_link :0
---->sh_info :0
---->sh_addralign :1
---->sh_entsize :1

Section[23] : 0x00000007
---->sh_name :.note.gnu.gold-version
---->sh_flags :0x00000000
---->sh_offset :980508
---->sh_size :28
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[24] : 0x70000003
---->sh_name :.ARM.attributes
---->sh_flags :0x00000000
---->sh_offset :980536
---->sh_size :56
---->sh_link :0
---->sh_info :0
---->sh_addralign :1
---->sh_entsize :0

Section[25] : 0x00000003
---->sh_name :.shstrtab
---->sh_flags :0x00000000
---->sh_offset :980592
---->sh_size :259
---->sh_link :0
---->sh_info :0
---->sh_addralign :1
---->sh_entsize :0


07/30/2025 15:13:56 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:56 [DEBUG] [filename:protect_so.py] [line:43] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\protectSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/arm64-v8a/libegis.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/arm64-v8a/libegis.so 
07/30/2025 15:13:57 [DEBUG] [filename:execute_cmd.py] [line:49] File size is 1494696
this is 64 bit library
--------->DT_STRTAB : 0x6fffffea20f8


sym_index : 1

sym_index : 2

sym_index : 3

sym_index : 4

sym_index : 5

sym_index : 6

sym_index : 7

sym_index : 8

sym_index : 9

sym_index : 10

sym_index : 11

sym_index : 12

sym_index : 13

sym_index : 14

sym_index : 15

sym_index : 16

sym_index : 17

sym_index : 18

sym_index : 19

sym_index : 20

sym_index : 21

sym_index : 22

sym_index : 23

sym_index : 24

sym_index : 25

sym_index : 26

sym_index : 27

sym_index : 28

sym_index : 29

sym_index : 30

sym_index : 31

sym_index : 32

sym_index : 33

sym_index : 34

sym_index : 35

sym_index : 36

sym_index : 37

sym_index : 38

sym_index : 39

sym_index : 40

sym_index : 41

sym_index : 42

sym_index : 43

sym_index : 44

sym_index : 45

sym_index : 46

sym_index : 47

sym_index : 48

sym_index : 49

sym_index : 50

sym_index : 51

sym_index : 52

sym_index : 53

sym_index : 54

sym_index : 55

sym_index : 56

sym_index : 57

sym_index : 58

sym_index : 59

sym_index : 60

sym_index : 61

sym_index : 62

sym_index : 63

sym_index : 64

sym_index : 65

sym_index : 66

sym_index : 67

sym_index : 68

sym_index : 69

sym_index : 70

sym_index : 71

sym_index : 72

sym_index : 73

sym_index : 74

sym_index : 75

sym_index : 76

sym_index : 77

sym_index : 78

sym_index : 79

sym_index : 80

sym_index : 81

sym_index : 82

sym_index : 83

sym_index : 84

sym_index : 85

sym_index : 86

sym_index : 87

sym_index : 88

sym_index : 89

sym_index : 90

sym_index : 91

sym_index : 92

sym_index : 93

sym_index : 94

sym_index : 95

sym_index : 96

sym_index : 97

sym_index : 98

sym_index : 99

sym_index : 100

sym_index : 101

sym_index : 102

sym_index : 103

sym_index : 104

sym_index : 105

sym_index : 106

sym_index : 107

sym_index : 108

sym_index : 109

sym_index : 110

sym_index : 111

sym_index : 112

sym_index : 113

sym_index : 114

sym_index : 115

sym_index : 116

sym_index : 117

sym_index : 118

sym_index : 119

sym_index : 120

sym_index : 121

sym_index : 122

sym_index : 123

sym_index : 124

sym_index : 125

sym_index : 126

sym_index : 127

sym_index : 128

sym_index : 129

sym_index : 130

07/30/2025 15:13:57 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:57 [DEBUG] [filename:protect_so.py] [line:51] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\protectSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86/libegis-x86.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86/libegis-x86.so 
07/30/2025 15:13:57 [DEBUG] [filename:execute_cmd.py] [line:49] File size is 1543580
this is 32 bit library
Ident : ELF
Type  : 3
Machine : 3
Version : 1
Entry point address: 0x00000000
Start of program headers: 52
Start of section headers: 1542500
Flags : 0x00000000
Size of this header: : 52
Size of program headers: 32
Number of program headers: : 8
Size of section headers: : 40
Number of section headers: 27
Section header string table index: 26

------>Dynamic [0] :0x00000003
------>Dynamic [1] :0x00000002
------>Dynamic [2] :0x00000017
------>Dynamic [3] :0x00000014
------>Dynamic [4] :0x00000011
------>Dynamic [5] :0x00000012
------>Dynamic [6] :0x00000013
------>Dynamic [7] :0x6ffffffa
------>Dynamic [8] :0x00000006
------>Dynamic [9] :0x0000000b
------>Dynamic [10] :0x00000005
--------->DT_STRTAB : 0x6fffffe88908
------>Dynamic [11] :0x0000000a
------>Dynamic [12] :0x00000004
------>Dynamic [13] :0x00000001
------>Dynamic [14] :0x00000001
------>Dynamic [15] :0x00000001
------>Dynamic [16] :0x00000001
------>Dynamic [17] :0x00000001
------>Dynamic [18] :0x00000001
------>Dynamic [19] :0x0000000e
------>Dynamic [20] :0x0000001a
------>Dynamic [21] :0x0000001c
------>Dynamic [22] :0x00000019
------>Dynamic [23] :0x0000001b
------>Dynamic [24] :0x0000001e
------>Dynamic [25] :0x6ffffffb
------>Dynamic [26] :0x6ffffff0
------>Dynamic [27] :0x6ffffffc
------>Dynamic [28] :0x6ffffffd
------>Dynamic [29] :0x6ffffffe
------>Dynamic [30] :0x6fffffff
------>Dynamic [31] :0x00000000
------>Dynamic [32] :0x00000000
------>Dynamic [33] :0x00000000
------>Dynamic [34] :0x00000000
------>Dynamic [35] :0x00000000
------>Dynamic [36] :0x00000000

Section[0] : 0x00000000
---->sh_name :
---->sh_flags :0x00000000
---->sh_offset :0
---->sh_size :0
---->sh_link :0
---->sh_info :0
---->sh_addralign :0
---->sh_entsize :0

Section[1] : 0x00000007
---->sh_name :.note.gnu.build-id
---->sh_flags :0x00000002
---->sh_offset :308
---->sh_size :36
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[2] : 0x0000000b
---->sh_name :.dynsym
---->sh_flags :0x00000002
---->sh_offset :344
---->sh_size :34720
---->sh_link :3
---->sh_info :1
---->sh_addralign :4
---->sh_entsize :16

------>All Symbols[004] : JNI_OnLoad                               | 34d30     (offset) | 2bc       (code) | 11        (index) 

sym_index : 1
------>All Symbols[221] : tl_mmap                                  | 99554     (offset) | 3e        (code) | 11        (index) 

sym_index : 2
Section[3] : 0x00000003
---->sh_name :.dynstr
---->sh_flags :0x00000002
---->sh_offset :35064
---->sh_size :100835
---->sh_link :0
---->sh_info :0
---->sh_addralign :1
---->sh_entsize :0

Section[4] : 0x00000005
---->sh_name :.hash
---->sh_flags :0x00000002
---->sh_offset :135900
---->sh_size :16900
---->sh_link :2
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :4

Section[5] : 0x6fffffff
---->sh_name :.gnu.version
---->sh_flags :0x00000002
---->sh_offset :152800
---->sh_size :4340
---->sh_link :2
---->sh_info :0
---->sh_addralign :2
---->sh_entsize :2

Section[6] : 0x6ffffffd
---->sh_name :.gnu.version_d
---->sh_flags :0x00000002
---->sh_offset :157140
---->sh_size :28
---->sh_link :3
---->sh_info :1
---->sh_addralign :4
---->sh_entsize :0

Section[7] : 0x6ffffffe
---->sh_name :.gnu.version_r
---->sh_flags :0x00000002
---->sh_offset :157168
---->sh_size :96
---->sh_link :3
---->sh_info :3
---->sh_addralign :4
---->sh_entsize :0

Section[8] : 0x00000009
---->sh_name :.rel.dyn
---->sh_flags :0x00000002
---->sh_offset :157264
---->sh_size :24328
---->sh_link :2
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :8

Section[9] : 0x00000009
---->sh_name :.rel.plt
---->sh_flags :0x00000042
---->sh_offset :181592
---->sh_size :7456
---->sh_link :2
---->sh_info :10
---->sh_addralign :4
---->sh_entsize :8

Section[10] : 0x00000001
---->sh_name :.plt
---->sh_flags :0x00000006
---->sh_offset :189056
---->sh_size :14928
---->sh_link :0
---->sh_info :0
---->sh_addralign :16
---->sh_entsize :4

Section[11] : 0x00000001
---->sh_name :.text
---->sh_flags :0x00000006
---->sh_offset :203984
---->sh_size :1045150
---->sh_link :0
---->sh_info :0
---->sh_addralign :16
---->sh_entsize :0

Section[12] : 0x00000001
---->sh_name :.rodata
---->sh_flags :0x00000002
---->sh_offset :1249136
---->sh_size :91184
---->sh_link :0
---->sh_info :0
---->sh_addralign :16
---->sh_entsize :0

Section[13] : 0x00000001
---->sh_name :.gcc_except_table
---->sh_flags :0x00000002
---->sh_offset :1340320
---->sh_size :39156
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[14] : 0x00000001
---->sh_name :.eh_frame
---->sh_flags :0x00000002
---->sh_offset :1379476
---->sh_size :107160
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[15] : 0x00000001
---->sh_name :.eh_frame_hdr
---->sh_flags :0x00000002
---->sh_offset :1486636
---->sh_size :26820
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[16] : 0x0000000f
---->sh_name :.fini_array
---->sh_flags :0x00000003
---->sh_offset :1517376
---->sh_size :8
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[17] : 0x00000001
---->sh_name :.data.rel.ro
---->sh_flags :0x00000003
---->sh_offset :1517392
---->sh_size :13852
---->sh_link :0
---->sh_info :0
---->sh_addralign :16
---->sh_entsize :0

Section[18] : 0x0000000e
---->sh_name :.init_array
---->sh_flags :0x00000003
---->sh_offset :1531244
---->sh_size :44
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[19] : 0x00000006
---->sh_name :.dynamic
---->sh_flags :0x00000003
---->sh_offset :1531288
---->sh_size :296
---->sh_link :3
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :8

Section[20] : 0x00000001
---->sh_name :.got
---->sh_flags :0x00000003
---->sh_offset :1531584
---->sh_size :664
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[21] : 0x00000001
---->sh_name :.got.plt
---->sh_flags :0x00000003
---->sh_offset :1532248
---->sh_size :3740
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[22] : 0x00000001
---->sh_name :.data
---->sh_flags :0x00000003
---->sh_offset :1536000
---->sh_size :6096
---->sh_link :0
---->sh_info :0
---->sh_addralign :8
---->sh_entsize :0

Section[23] : 0x00000008
---->sh_name :.bss
---->sh_flags :0x00000003
---->sh_offset :1542096
---->sh_size :4688
---->sh_link :0
---->sh_info :0
---->sh_addralign :64
---->sh_entsize :0

Section[24] : 0x00000001
---->sh_name :.comment
---->sh_flags :0x00000030
---->sh_offset :1542096
---->sh_size :101
---->sh_link :0
---->sh_info :0
---->sh_addralign :1
---->sh_entsize :1

Section[25] : 0x00000007
---->sh_name :.note.gnu.gold-version
---->sh_flags :0x00000000
---->sh_offset :1542200
---->sh_size :28
---->sh_link :0
---->sh_info :0
---->sh_addralign :4
---->sh_entsize :0

Section[26] : 0x00000003
---->sh_name :.shstrtab
---->sh_flags :0x00000000
---->sh_offset :1542228
---->sh_size :272
---->sh_link :0
---->sh_info :0
---->sh_addralign :1
---->sh_entsize :0


07/30/2025 15:13:57 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:57 [DEBUG] [filename:protect_so.py] [line:59] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\protectSo.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86_64/libegis-x86.so D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86_64/libegis-x86.so 
07/30/2025 15:13:57 [DEBUG] [filename:execute_cmd.py] [line:49] File size is 1577480
this is 64 bit library
--------->DT_STRTAB : 0x6fffffe7cf58


sym_index : 1

07/30/2025 15:13:57 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:57 [INFO] [filename:main.py] [line:1293] protect libegis.so successed!
07/30/2025 15:13:57 [DEBUG] [filename:upx_protect_so.py] [line:37] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\upx_payegis_windows.upx.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi/libegis.so 
07/30/2025 15:13:57 [DEBUG] [filename:execute_cmd.py] [line:49]         File size         Ratio      Format      Name

   --------------------   ------   -----------   -----------

   1072000 ->    677300   63.18%    linux/arm    libegis.so



Packed 1 file.


07/30/2025 15:13:57 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:57 [INFO] [filename:upx_protect_so.py] [line:39] Reslut0
07/30/2025 15:13:57 [DEBUG] [filename:upx_protect_so.py] [line:44] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\protectUpx.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi/libegis.so 
07/30/2025 15:13:58 [DEBUG] [filename:execute_cmd.py] [line:49] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi/libegis.so size is 677300
32 bit so

07/30/2025 15:13:58 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:58 [DEBUG] [filename:upx_protect_so.py] [line:51] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\upx_payegis_windows.upx.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi-v7a/libegis.so 
07/30/2025 15:13:58 [DEBUG] [filename:execute_cmd.py] [line:49]         File size         Ratio      Format      Name

   --------------------   ------   -----------   -----------

    981892 ->    640436   65.22%    linux/arm    libegis.so



Packed 1 file.


07/30/2025 15:13:58 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:58 [DEBUG] [filename:upx_protect_so.py] [line:57] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\protectUpx.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi-v7a/libegis.so 
07/30/2025 15:13:58 [DEBUG] [filename:execute_cmd.py] [line:49] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/armeabi-v7a/libegis.so size is 640436
32 bit so

07/30/2025 15:13:58 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:58 [DEBUG] [filename:upx_protect_so.py] [line:78] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\upx_payegis_windows.upx.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86/libegis-x86.so 
07/30/2025 15:13:59 [DEBUG] [filename:execute_cmd.py] [line:49]         File size         Ratio      Format      Name

   --------------------   ------   -----------   -----------

   1543580 ->    698320   45.24%   linux/i386    libegis-x86.so



Packed 1 file.


07/30/2025 15:13:59 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:59 [DEBUG] [filename:upx_protect_so.py] [line:84] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\bin\tmp\protectUpx.out.exe D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86/libegis-x86.so 
07/30/2025 15:13:59 [DEBUG] [filename:execute_cmd.py] [line:49] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp/x86/libegis-x86.so size is 698320
32 bit so

07/30/2025 15:13:59 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:13:59 [INFO] [filename:main.py] [line:1304] upx protect libegis.so successed!
07/30/2025 15:13:59 [DEBUG] [filename:add_egis_so.py] [line:45] add_egis_so
07/30/2025 15:13:59 [DEBUG] [filename:add_egis_so.py] [line:82] arm64 exist
07/30/2025 15:13:59 [DEBUG] [filename:add_egis_so.py] [line:76] armeabi-v7 exist
07/30/2025 15:13:59 [DEBUG] [filename:add_egis_so.py] [line:79] x86 exist
07/30/2025 15:13:59 [DEBUG] [filename:add_egis_so.py] [line:85] x86_64 exist
07/30/2025 15:13:59 [DEBUG] [filename:add_egis_so.py] [line:95] add arm64-v8a
07/30/2025 15:13:59 [DEBUG] [filename:apk.py] [line:153] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp\arm64-v8a\libegis.so
07/30/2025 15:13:59 [DEBUG] [filename:apk.py] [line:156] lib/arm64-v8a/libegis.so
07/30/2025 15:13:59 [DEBUG] [filename:add_egis_so.py] [line:99] add x86_64
07/30/2025 15:13:59 [DEBUG] [filename:apk.py] [line:153] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp\x86_64\libegis-x86.so
07/30/2025 15:13:59 [DEBUG] [filename:apk.py] [line:156] lib/x86_64/libegis-x86.so
07/30/2025 15:13:59 [DEBUG] [filename:add_egis_so.py] [line:103] add x86/libegis-x86
07/30/2025 15:13:59 [DEBUG] [filename:apk.py] [line:171] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp\x86\libegis-x86.so
07/30/2025 15:13:59 [DEBUG] [filename:apk.py] [line:174] lib\x86\libegis-x86.so
07/30/2025 15:13:59 [DEBUG] [filename:apk.py] [line:178] lib\x86/libegis-x86.so
07/30/2025 15:13:59 [DEBUG] [filename:add_egis_so.py] [line:113] add armeabi-v7
07/30/2025 15:13:59 [DEBUG] [filename:apk.py] [line:153] D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\lib_tmp\armeabi-v7a\libegis.so
07/30/2025 15:13:59 [DEBUG] [filename:apk.py] [line:156] lib/armeabi-v7a/libegis.so
07/30/2025 15:13:59 [DEBUG] [filename:apk.py] [line:17] zip -d D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk classes2.dex
07/30/2025 15:14:00 [DEBUG] [filename:execute_cmd.py] [line:49] deleting: classes2.dex

07/30/2025 15:14:00 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:14:00 [DEBUG] [filename:apk.py] [line:21] delete the file classes2.dex successed!
07/30/2025 15:14:00 [DEBUG] [filename:apk.py] [line:17] zip -d D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk classes.dex
07/30/2025 15:14:00 [DEBUG] [filename:execute_cmd.py] [line:49] deleting: classes.dex

07/30/2025 15:14:00 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:14:00 [DEBUG] [filename:apk.py] [line:21] delete the file classes.dex successed!
07/30/2025 15:14:00 [DEBUG] [filename:apk.py] [line:212] zipalign D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk to D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed-zipalign.apk
07/30/2025 15:14:00 [DEBUG] [filename:apk.py] [line:214] zipalign -v -f 4  D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed-zipalign.apk
07/30/2025 15:14:00 [DEBUG] [filename:execute_cmd.py] [line:49] Verifying alignment of D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed-zipalign.apk (4)...

      87 META-INF/com/android/build/gradle/app-metadata.properties (OK - compressed)

     208 META-INF/version-control-info.textproto (OK - compressed)

     316 assets/dexopt/baseline.prof (OK)

    2144 assets/dexopt/baseline.profm (OK)

   10976 lib/arm64-v8a/libnative-lib.so (OK)

 1043168 lib/armeabi-v7a/libnative-lib.so (OK)

 1845984 lib/x86/libnative-lib.so (OK)

 2845408 lib/x86_64/libnative-lib.so (OK)

 3840271 DebugProbesKt.bin (OK - compressed)

 3841128 META-INF/androidx.activity_activity.version (OK)

 3841224 META-INF/androidx.annotation_annotation-experimental.version (OK)

 3841316 META-INF/androidx.appcompat_appcompat-resources.version (OK)

 3841400 META-INF/androidx.appcompat_appcompat.version (OK)

 3841484 META-INF/androidx.arch.core_core-runtime.version (OK - compressed)

 3841620 META-INF/androidx.cardview_cardview.version (OK)

 3841720 META-INF/androidx.coordinatorlayout_coordinatorlayout.version (OK)

 3841796 META-INF/androidx.core_core-ktx.version (OK)

 3841868 META-INF/androidx.core_core.version (OK)

 3841960 META-INF/androidx.cursoradapter_cursoradapter.version (OK)

 3842044 META-INF/androidx.customview_customview.version (OK)

 3842132 META-INF/androidx.documentfile_documentfile.version (OK)

 3842220 META-INF/androidx.drawerlayout_drawerlayout.version (OK)

 3842316 META-INF/androidx.dynamicanimation_dynamicanimation.version (OK)

 3842404 META-INF/androidx.emoji2_emoji2-views-helper.version (OK)

 3842480 META-INF/androidx.emoji2_emoji2.version (OK)

 3842560 META-INF/androidx.fragment_fragment.version (OK)

 3842648 META-INF/androidx.interpolator_interpolator.version (OK)

 3842744 META-INF/androidx.legacy_legacy-support-core-utils.version (OK)

 3842839 META-INF/androidx.lifecycle_lifecycle-livedata-core.version (OK - compressed)

 3842990 META-INF/androidx.lifecycle_lifecycle-livedata.version (OK - compressed)

 3843135 META-INF/androidx.lifecycle_lifecycle-process.version (OK - compressed)

 3843278 META-INF/androidx.lifecycle_lifecycle-runtime.version (OK - compressed)

 3843435 META-INF/androidx.lifecycle_lifecycle-viewmodel-savedstate.version (OK - compressed)

 3843594 META-INF/androidx.lifecycle_lifecycle-viewmodel.version (OK - compressed)

 3843728 META-INF/androidx.loader_loader.version (OK)

 3843836 META-INF/androidx.localbroadcastmanager_localbroadcastmanager.version (OK)

 3843912 META-INF/androidx.print_print.version (OK)

 3844008 META-INF/androidx.profileinstaller_profileinstaller.version (OK)

 3844096 META-INF/androidx.recyclerview_recyclerview.version (OK)

 3844180 META-INF/androidx.savedstate_savedstate.version (OK)

 3844268 META-INF/androidx.startup_startup-runtime.version (OK)

 3844348 META-INF/androidx.tracing_tracing.version (OK)

 3844432 META-INF/androidx.transition_transition.version (OK)

 3844532 META-INF/androidx.vectordrawable_vectordrawable-animated.version (OK)

 3844624 META-INF/androidx.vectordrawable_vectordrawable.version (OK)

 3844728 META-INF/androidx.versionedparcelable_versionedparcelable.version (OK)

 3844812 META-INF/androidx.viewpager2_viewpager2.version (OK)

 3844896 META-INF/androidx.viewpager_viewpager.version (OK)

 3844988 META-INF/com.google.android.material_material.version (OK)

 3845068 META-INF/kotlinx_coroutines_android.version (OK)

 3845144 META-INF/kotlinx_coroutines_core.version (OK)

 3845241 META-INF/services/kotlinx.coroutines.CoroutineExceptionHandler (OK - compressed)

 3845388 META-INF/services/kotlinx.coroutines.internal.MainDispatcherFactory (OK - compressed)

 3845511 kotlin/annotation/annotation.kotlin_builtins (OK - compressed)

 3846146 kotlin/collections/collections.kotlin_builtins (OK - compressed)

 3847741 kotlin/coroutines/coroutines.kotlin_builtins (OK - compressed)

 3847968 kotlin/internal/internal.kotlin_builtins (OK - compressed)

 3848421 kotlin/kotlin.kotlin_builtins (OK - compressed)

 3853491 kotlin/ranges/ranges.kotlin_builtins (OK - compressed)

 3854823 kotlin/reflect/reflect.kotlin_builtins (OK - compressed)

 3856128 res/-1.xml (OK - compressed)

 3856511 res/-5.xml (OK - compressed)

 3856764 res/-6.webp (OK)

 3886432 res/-7.xml (OK - compressed)

 3886848 res/-B.png (OK)

 3887169 res/-B.xml (OK - compressed)

 3887512 res/-N.png (OK)

 3887870 res/-Q.xml (OK - compressed)

 3888209 res/0C.xml (OK - compressed)

 3888630 res/0K.xml (OK - compressed)

 3888892 res/0M.xml (OK - compressed)

 3889276 res/0c.9.png (OK)

 3889583 res/0w.xml (OK - compressed)

 3890604 res/0x.9.png (OK)

 3890856 res/13.webp (OK)

 3902796 res/1I.9.png (OK)

 3905144 res/1J.9.png (OK)

 3906915 res/1R.xml (OK - compressed)

 3907232 res/1e.9.png (OK)

 3907443 res/1v.xml (OK - compressed)

 3907862 res/20.xml (OK - compressed)

 3908200 res/21.xml (OK - compressed)

 3908552 res/27.xml (OK - compressed)

 3908892 res/271.xml (OK - compressed)

 3909106 res/2F.xml (OK - compressed)

 3909432 res/2K.9.png (OK)

 3909700 res/2P.png (OK)

 3910002 res/2R.xml (OK - compressed)

 3910564 res/2d.png (OK)

 3910749 res/2f.xml (OK - compressed)

 3911458 res/2i.xml (OK - compressed)

 3911706 res/2j.xml (OK - compressed)

 3912554 res/2n.xml (OK - compressed)

 3913132 res/2w.xml (OK - compressed)

 3913674 res/2x.xml (OK - compressed)

 3914052 res/33.9.png (OK)

 3914633 res/3A.xml (OK - compressed)

 3915173 res/3R.xml (OK - compressed)

 3915518 res/3h.xml (OK - compressed)

 3915908 res/3u.9.png (OK)

 3916208 res/42.9.png (OK)

 3916462 res/46.xml (OK - compressed)

 3916884 res/49.png (OK)

 3917519 res/4B.xml (OK - compressed)

 3917824 res/4H.xml (OK - compressed)

 3918176 res/4I.xml (OK - compressed)

 3918577 res/4P.xml (OK - compressed)

 3918893 res/4Q.xml (OK - compressed)

 3919144 res/4S.xml (OK - compressed)

 3919565 res/4_.xml (OK - compressed)

 3919987 res/4j.xml (OK - compressed)

 3920140 res/4k.png (OK)

 3920381 res/4o.xml (OK - compressed)

 3920740 res/4u.xml (OK - compressed)

 3921070 res/4x.xml (OK - compressed)

 3921528 res/51.xml (OK - compressed)

 3921941 res/59.xml (OK - compressed)

 3922300 res/5D.9.png (OK)

 3922612 res/5T.xml (OK - compressed)

 3923032 res/5U.png (OK)

 3923468 res/5Y.xml (OK - compressed)

 3923832 res/5c.png (OK)

 3924012 res/5c.webp (OK)

 4014376 res/5l.xml (OK - compressed)

 4014782 res/5z.xml (OK - compressed)

 4015217 res/61.xml (OK - compressed)

 4015616 res/62.9.png (OK)

 4015868 res/65.9.png (OK)

 4016143 res/66.xml (OK - compressed)

 4016603 res/68.xml (OK - compressed)

 4016959 res/6Q.xml (OK - compressed)

 4017300 res/6t.png (OK)

 4017567 res/6x.xml (OK - compressed)

 4017868 res/7C.9.png (OK)

 4018338 res/7G.xml (OK - compressed)

 4018901 res/7H.xml (OK - compressed)

 4019176 res/7I.9.png (OK)

 4019561 res/7N.xml (OK - compressed)

 4019786 res/7R.xml (OK - compressed)

 4019996 res/7_.9.png (OK)

 4020852 res/7i.png (OK)

 4021360 res/7o.9.png (OK)

 4021925 res/7s.xml (OK - compressed)

 4022250 res/7s1.xml (OK - compressed)

 4022585 res/80.xml (OK - compressed)

 4023468 res/8h.9.png (OK)

 4023716 res/8h.png (OK)

 4024702 res/8s.xml (OK - compressed)

 4025085 res/8y.xml (OK - compressed)

 4025576 res/9N.9.png (OK)

 4025803 res/9O.xml (OK - compressed)

 4026326 res/9P.xml (OK - compressed)

 4026700 res/9Q.webp (OK)

 4047098 res/9T.xml (OK - compressed)

 4047533 res/9T1.xml (OK - compressed)

 4048002 res/9T2.xml (OK - compressed)

 4048415 res/9V.xml (OK - compressed)

 4049104 res/9X.9.png (OK)

 4049336 res/9m.xml (OK - compressed)

 4049552 res/9n.9.png (OK)

 4049770 res/9p.xml (OK - compressed)

 4050072 res/9z.png (OK)

 4050705 res/9z.xml (OK - compressed)

 4050975 res/A0.xml (OK - compressed)

 4051685 res/A1.xml (OK - compressed)

 4052114 res/A4.xml (OK - compressed)

 4052455 res/A5.xml (OK - compressed)

 4053113 res/AB.xml (OK - compressed)

 4053820 res/Aa.xml (OK - compressed)

 4054187 res/B6.xml (OK - compressed)

 4054544 res/BG.9.png (OK)

 4054774 res/BJ.xml (OK - compressed)

 4055155 res/BJ1.xml (OK - compressed)

 4055592 res/BL.9.png (OK)

 4055864 res/BM.png (OK)

 4056063 res/BT.xml (OK - compressed)

 4056416 res/BW.xml (OK - compressed)

 4056678 res/Bd.xml (OK - compressed)

 4056936 res/Be.xml (OK - compressed)

 4057813 res/By.xml (OK - compressed)

 4058172 res/By1.xml (OK - compressed)

 4058472 res/CK.9.png (OK)

 4058760 res/C_.9.png (OK)

 4059015 res/Cg.xml (OK - compressed)

 4059255 res/D4.xml (OK - compressed)

 4059646 res/D5.xml (OK - compressed)

 4060139 res/D6.xml (OK - compressed)

 4060495 res/DG.xml (OK - compressed)

 4060872 res/DL.9.png (OK)

 4061090 res/DS.xml (OK - compressed)

 4061346 res/DV.xml (OK - compressed)

 4061776 res/DZ.xml (OK - compressed)

 4062008 res/D_.9.png (OK)

 4062228 res/EA.9.png (OK)

 4062468 res/EP.png (OK)

 4062818 res/EZ.xml (OK - compressed)

 4063086 res/Eg.xml (OK - compressed)

 4063677 res/F8.xml (OK - compressed)

 4064315 res/F81.xml (OK - compressed)

 4064693 res/FS.xml (OK - compressed)

 4065034 res/FT.xml (OK - compressed)

 4065644 res/FW.png (OK)

 4065991 res/Fd.xml (OK - compressed)

 4066378 res/Fq.xml (OK - compressed)

 4066596 res/Fu.xml (OK - compressed)

 4067188 res/G2.9.png (OK)

 4068471 res/G2.xml (OK - compressed)

 4068976 res/GC.xml (OK - compressed)

 4069472 res/GD.xml (OK - compressed)

 4070066 res/GF.xml (OK - compressed)

 4070356 res/GK.xml (OK - compressed)

 4070859 res/GQ.xml (OK - compressed)

 4071236 res/GR.xml (OK - compressed)

 4071746 res/GT.xml (OK - compressed)

 4072056 res/Gf.png (OK)

 4073084 res/Gt.9.png (OK)

 4073326 res/Gt.xml (OK - compressed)

 4073688 res/H-.png (OK)

 4073931 res/HC.xml (OK - compressed)

 4074283 res/HQ.xml (OK - compressed)

 4074743 res/Ha.xml (OK - compressed)

 4075100 res/Hd.xml (OK - compressed)

 4075411 res/I3.xml (OK - compressed)

 4075706 res/IR.xml (OK - compressed)

 4076112 res/IX.9.png (OK)

 4077696 res/Ib.xml (OK - compressed)

 4078097 res/Id.xml (OK - compressed)

 4078393 res/In.xml (OK - compressed)

 4078665 res/It.xml (OK - compressed)

 4079705 res/Ix.xml (OK - compressed)

 4080051 res/J7.xml (OK - compressed)

 4080525 res/JD.xml (OK - compressed)

 4081189 res/JD1.xml (OK - compressed)

 4081558 res/JF.xml (OK - compressed)

 4081776 res/JJ.9.png (OK)

 4082096 res/JQ.xml (OK - compressed)

 4082516 res/JT.xml (OK - compressed)

 4082873 res/JT1.xml (OK - compressed)

 4083304 res/Jl.xml (OK - compressed)

 4083575 res/Jw.xml (OK - compressed)

 4084394 res/K2.xml (OK - compressed)

 4084640 res/K5.xml (OK - compressed)

 4085336 res/K51.xml (OK - compressed)

 4085812 res/KH.9.png (OK)

 4086472 res/KM.png (OK)

 4086927 res/KT.xml (OK - compressed)

 4087288 res/K_.9.png (OK)

 4087544 res/Ke.xml (OK - compressed)

 4088244 res/L-.xml (OK - compressed)

 4089285 res/LJ.xml (OK - compressed)

 4089606 res/LT.xml (OK - compressed)

 4089962 res/L_.xml (OK - compressed)

 4090341 res/Lf.xml (OK - compressed)

 4090804 res/Li.9.png (OK)

 4092623 res/Lo.xml (OK - compressed)

 4092962 res/Lt.xml (OK - compressed)

 4093318 res/Lv.xml (OK - compressed)

 4093433 res/M2.xml (OK - compressed)

 4093775 res/M5.xml (OK - compressed)

 4094180 res/M7.xml (OK - compressed)

 4094620 res/MF.9.png (OK)

 4097480 res/MO.webp (OK)

 4101864 res/MO.xml (OK - compressed)

 4102104 res/MQ.png (OK)

 4102657 res/MU.xml (OK - compressed)

 4102993 res/MZ.xml (OK - compressed)

 4103348 res/Ma.9.png (OK)

 4103635 res/Mp.xml (OK - compressed)

 4104051 res/Mt.xml (OK - compressed)

 4104609 res/N0.xml (OK - compressed)

 4104976 res/NA.9.png (OK)

 4107521 res/NB.xml (OK - compressed)

 4107772 res/NF.xml (OK - compressed)

 4108004 res/NG.png (OK)

 4108366 res/NM.xml (OK - compressed)

 4108628 res/NN.xml (OK - compressed)

 4109080 res/NN1.xml (OK - compressed)

 4109332 res/NZ.9.png (OK)

 4109556 res/Nk.9.png (OK)

 4110376 res/No.9.png (OK)

 4110616 res/Nt.webp (OK)

 4116494 res/Nu.xml (OK - compressed)

 4117237 res/Ny.xml (OK - compressed)

 4117572 res/OH.xml (OK - compressed)

 4117822 res/OX.xml (OK - compressed)

 4118178 res/Ol.xml (OK - compressed)

 4118896 res/Ox.xml (OK - compressed)

 4119220 res/PQ.xml (OK - compressed)

 4119573 res/PV.xml (OK - compressed)

 4120049 res/PX.xml (OK - compressed)

 4120472 res/Pa.9.png (OK)

 4121252 res/Pb.png (OK)

 4121928 res/Pg.9.png (OK)

 4122175 res/QD.xml (OK - compressed)

 4122540 res/QH.xml (OK - compressed)

 4122872 res/QJ.9.png (OK)

 4123101 res/QN.xml (OK - compressed)

 4123450 res/QN1.xml (OK - compressed)

 4123774 res/QZ.xml (OK - compressed)

 4124158 res/QZ1.xml (OK - compressed)

 4124266 res/Qd.xml (OK - compressed)

 4124741 res/Qp.xml (OK - compressed)

 4125094 res/Qq.xml (OK - compressed)

 4125210 res/Qr.xml (OK - compressed)

 4125954 res/Qt.xml (OK - compressed)

 4126508 res/Qu.xml (OK - compressed)

 4127020 res/R2.xml (OK - compressed)

 4127376 res/RD.xml (OK - compressed)

 4127643 res/RH.xml (OK - compressed)

 4128077 res/RI.xml (OK - compressed)

 4128357 res/RM.xml (OK - compressed)

 4128740 res/RV.png (OK)

 4129343 res/Ro.xml (OK - compressed)

 4129902 res/S6.xml (OK - compressed)

 4130253 res/S8.xml (OK - compressed)

 4130779 res/SG.xml (OK - compressed)

 4131052 res/SV.9.png (OK)

 4131303 res/Sc.xml (OK - compressed)

 4131736 res/Sn.webp (OK)

 4145238 res/Sr.xml (OK - compressed)

 4145696 res/Su.9.png (OK)

 4145914 res/T4.xml (OK - compressed)

 4146466 res/TB.xml (OK - compressed)

 4146720 res/TH.xml (OK - compressed)

 4147024 res/TJ.xml (OK - compressed)

 4147538 res/Tf.xml (OK - compressed)

 4147880 res/Th.png (OK)

 4148708 res/Tj.9.png (OK)

 4149075 res/Tm.xml (OK - compressed)

 4149557 res/Tn.xml (OK - compressed)

 4149992 res/U-.9.png (OK)

 4150255 res/U0.xml (OK - compressed)

 4150361 res/U7.xml (OK - compressed)

 4151076 res/U8.xml (OK - compressed)

 4151952 res/UE.9.png (OK)

 4152214 res/UE.xml (OK - compressed)

 4152584 res/UP.xml (OK - compressed)

 4153028 res/UR.png (OK)

 4153490 res/UX.xml (OK - compressed)

 4153789 res/Uf.xml (OK - compressed)

 4154436 res/V1.xml (OK - compressed)

 4154921 res/V3.xml (OK - compressed)

 4155296 res/V7.xml (OK - compressed)

 4155511 res/VM.xml (OK - compressed)

 4155741 res/VN.xml (OK - compressed)

 4156141 res/VT.xml (OK - compressed)

 4156536 res/W4.9.png (OK)

 4156793 res/WI.xml (OK - compressed)

 4156988 res/WK.xml (OK - compressed)

 4157404 res/Wh.png (OK)

 4157616 res/Wr.png (OK)

 4157991 res/Ws.xml (OK - compressed)

 4158504 res/Wz.png (OK)

 4158744 res/X3.9.png (OK)

 4159008 res/X4.9.png (OK)

 4159235 res/XK.xml (OK - compressed)

 4159516 res/XW.xml (OK - compressed)

 4159624 res/XY.xml (OK - compressed)

 4159816 res/Xe.xml (OK - compressed)

 4160207 res/Xf.xml (OK - compressed)

 4160581 res/Xx.xml (OK - compressed)

 4160866 res/Xz.xml (OK - compressed)

 4161176 res/Y7.9.png (OK)

 4161432 res/YG.9.png (OK)

 4161675 res/YN.xml (OK - compressed)

 4162027 res/YW.xml (OK - compressed)

 4162253 res/YW1.xml (OK - compressed)

 4162610 res/Yc.xml (OK - compressed)

 4162952 res/Yt.9.png (OK)

 4163204 res/Yw.9.png (OK)

 4163432 res/Z8.png (OK)

 4163892 res/ZC.xml (OK - compressed)

 4164245 res/ZL.xml (OK - compressed)

 4164690 res/ZM.xml (OK - compressed)

 4165176 res/ZN.9.png (OK)

 4165445 res/ZN.xml (OK - compressed)

 4165978 res/ZW.xml (OK - compressed)

 4166883 res/Zd.xml (OK - compressed)

 4167239 res/Zg.xml (OK - compressed)

 4167659 res/_G.xml (OK - compressed)

 4167922 res/_I.xml (OK - compressed)

 4168242 res/_M.xml (OK - compressed)

 4168681 res/_o.xml (OK - compressed)

 4168908 res/_q.png (OK)

 4169121 res/_y.xml (OK - compressed)

 4169470 res/a0.xml (OK - compressed)

 4170048 res/a1.xml (OK - compressed)

 4170699 res/a5.xml (OK - compressed)

 4171056 res/a7.xml (OK - compressed)

 4171606 res/aG.xml (OK - compressed)

 4171932 res/aJ.xml (OK - compressed)

 4172188 res/aM.xml (OK - compressed)

 4172604 res/aT.xml (OK - compressed)

 4173076 res/aU.9.png (OK)

 4173564 res/aW.xml (OK - compressed)

 4173672 res/aa.xml (OK - compressed)

 4173924 res/ar.png (OK)

 4174355 res/ay.xml (OK - compressed)

 4174640 res/bL.xml (OK - compressed)

 4175103 res/bT.xml (OK - compressed)

 4175560 res/bX.9.png (OK)

 4175844 res/bb.xml (OK - compressed)

 4176130 res/bm.xml (OK - compressed)

 4176495 res/bt.xml (OK - compressed)

 4176965 res/c0.xml (OK - compressed)

 4177212 res/c2.xml (OK - compressed)

 4177585 res/c5.xml (OK - compressed)

 4177812 res/c6.xml (OK - compressed)

 4178038 res/cA.xml (OK - compressed)

 4178637 res/cL.xml (OK - compressed)

 4179038 res/cV.xml (OK - compressed)

 4179339 res/cc.xml (OK - compressed)

 4179723 res/cm.xml (OK - compressed)

 4180280 res/color-night-v8/material_timepicker_button_stroke.xml (OK - compressed)

 4180568 res/color-night-v8/material_timepicker_clockface.xml (OK - compressed)

 4180862 res/color-night-v8/material_timepicker_modebutton_tint.xml (OK - compressed)

 4181130 res/color-v23/abc_btn_colored_borderless_text_material.xml (OK - compressed)

 4181449 res/color-v23/abc_btn_colored_text_material.xml (OK - compressed)

 4181767 res/color-v23/abc_color_highlight_material.xml (OK - compressed)

 4182095 res/color-v23/abc_tint_btn_checkable.xml (OK - compressed)

 4182433 res/color-v23/abc_tint_default.xml (OK - compressed)

 4182868 res/color-v23/abc_tint_edittext.xml (OK - compressed)

 4183219 res/color-v23/abc_tint_seek_thumb.xml (OK - compressed)

 4183524 res/color-v23/abc_tint_spinner.xml (OK - compressed)

 4183877 res/color-v23/abc_tint_switch_track.xml (OK - compressed)

 4184249 res/color-v31/m3_dynamic_dark_default_color_primary_text.xml (OK - compressed)

 4184582 res/color-v31/m3_dynamic_dark_default_color_secondary_text.xml (OK - compressed)

 4184905 res/color-v31/m3_dynamic_dark_highlighted_text.xml (OK - compressed)

 4185186 res/color-v31/m3_dynamic_dark_hint_foreground.xml (OK - compressed)

 4185534 res/color-v31/m3_dynamic_dark_primary_text_disable_only.xml (OK - compressed)

 4185860 res/color-v31/m3_dynamic_default_color_primary_text.xml (OK - compressed)

 4186187 res/color-v31/m3_dynamic_default_color_secondary_text.xml (OK - compressed)

 4186505 res/color-v31/m3_dynamic_highlighted_text.xml (OK - compressed)

 4186781 res/color-v31/m3_dynamic_hint_foreground.xml (OK - compressed)

 4187124 res/color-v31/m3_dynamic_primary_text_disable_only.xml (OK - compressed)

 4187444 res/color-v31/m3_ref_palette_dynamic_neutral12.xml (OK - compressed)

 4187725 res/color-v31/m3_ref_palette_dynamic_neutral17.xml (OK - compressed)

 4188006 res/color-v31/m3_ref_palette_dynamic_neutral22.xml (OK - compressed)

 4188288 res/color-v31/m3_ref_palette_dynamic_neutral24.xml (OK - compressed)

 4188569 res/color-v31/m3_ref_palette_dynamic_neutral4.xml (OK - compressed)

 4188848 res/color-v31/m3_ref_palette_dynamic_neutral6.xml (OK - compressed)

 4189130 res/color-v31/m3_ref_palette_dynamic_neutral87.xml (OK - compressed)

 4189412 res/color-v31/m3_ref_palette_dynamic_neutral92.xml (OK - compressed)

 4189694 res/color-v31/m3_ref_palette_dynamic_neutral94.xml (OK - compressed)

 4189976 res/color-v31/m3_ref_palette_dynamic_neutral96.xml (OK - compressed)

 4190258 res/color-v31/m3_ref_palette_dynamic_neutral98.xml (OK - compressed)

 4190548 res/color-v31/m3_ref_palette_dynamic_neutral_variant12.xml (OK - compressed)

 4190837 res/color-v31/m3_ref_palette_dynamic_neutral_variant17.xml (OK - compressed)

 4191126 res/color-v31/m3_ref_palette_dynamic_neutral_variant22.xml (OK - compressed)

 4191415 res/color-v31/m3_ref_palette_dynamic_neutral_variant24.xml (OK - compressed)

 4191704 res/color-v31/m3_ref_palette_dynamic_neutral_variant4.xml (OK - compressed)

 4191991 res/color-v31/m3_ref_palette_dynamic_neutral_variant6.xml (OK - compressed)

 4192281 res/color-v31/m3_ref_palette_dynamic_neutral_variant87.xml (OK - compressed)

 4192570 res/color-v31/m3_ref_palette_dynamic_neutral_variant92.xml (OK - compressed)

 4192860 res/color-v31/m3_ref_palette_dynamic_neutral_variant94.xml (OK - compressed)

 4193150 res/color-v31/m3_ref_palette_dynamic_neutral_variant96.xml (OK - compressed)

 4193440 res/color-v31/m3_ref_palette_dynamic_neutral_variant98.xml (OK - compressed)

 4193734 res/color/abc_background_cache_hint_selector_material_dark.xml (OK - compressed)

 4194058 res/color/abc_background_cache_hint_selector_material_light.xml (OK - compressed)

 4194366 res/color/abc_hint_foreground_material_dark.xml (OK - compressed)

 4194706 res/color/abc_hint_foreground_material_light.xml (OK - compressed)

 4195055 res/color/abc_primary_text_disable_only_material_dark.xml (OK - compressed)

 4195371 res/color/abc_primary_text_disable_only_material_light.xml (OK - compressed)

 4195673 res/color/abc_primary_text_material_dark.xml (OK - compressed)

 4195978 res/color/abc_primary_text_material_light.xml (OK - compressed)

 4196271 res/color/abc_search_url_text.xml (OK - compressed)

 4196597 res/color/abc_secondary_text_material_dark.xml (OK - compressed)

 4196903 res/color/abc_secondary_text_material_light.xml (OK - compressed)

 4197199 res/color/design_box_stroke_color.xml (OK - compressed)

 4197541 res/color/design_error.xml (OK - compressed)

 4197831 res/color/design_icon_tint.xml (OK - compressed)

 4198105 res/color/m3_appbar_overlay_color.xml (OK - compressed)

 4198385 res/color/m3_assist_chip_icon_tint_color.xml (OK - compressed)

 4198699 res/color/m3_assist_chip_stroke_color.xml (OK - compressed)

 4199106 res/color/m3_bottom_sheet_drag_handle_color.xml (OK - compressed)

 4199364 res/color/m3_button_background_color_selector.xml (OK - compressed)

 4199686 res/color/m3_button_foreground_color_selector.xml (OK - compressed)

 4200005 res/color/m3_button_outline_color_selector.xml (OK - compressed)

 4200320 res/color/m3_button_ripple_color.xml (OK - compressed)

 4200696 res/color/m3_button_ripple_color_selector.xml (OK - compressed)

 4201073 res/color/m3_calendar_item_disabled_text.xml (OK - compressed)

 4201352 res/color/m3_calendar_item_stroke_color.xml (OK - compressed)

 4201702 res/color/m3_card_foreground_color.xml (OK - compressed)

 4202079 res/color/m3_card_ripple_color.xml (OK - compressed)

 4202549 res/color/m3_card_stroke_color.xml (OK - compressed)

 4203030 res/color/m3_checkbox_button_icon_tint.xml (OK - compressed)

 4203442 res/color/m3_checkbox_button_tint.xml (OK - compressed)

 4203879 res/color/m3_chip_assist_text_color.xml (OK - compressed)

 4204188 res/color/m3_chip_background_color.xml (OK - compressed)

 4204530 res/color/m3_chip_ripple_color.xml (OK - compressed)

 4205003 res/color/m3_chip_stroke_color.xml (OK - compressed)

 4205397 res/color/m3_chip_text_color.xml (OK - compressed)

 4205748 res/color/m3_dark_default_color_primary_text.xml (OK - compressed)

 4206068 res/color/m3_dark_default_color_secondary_text.xml (OK - compressed)

 4206378 res/color/m3_dark_highlighted_text.xml (OK - compressed)

 4206646 res/color/m3_dark_hint_foreground.xml (OK - compressed)

 4206983 res/color/m3_dark_primary_text_disable_only.xml (OK - compressed)

 4207297 res/color/m3_default_color_primary_text.xml (OK - compressed)

 4207613 res/color/m3_default_color_secondary_text.xml (OK - compressed)

 4207930 res/color/m3_efab_ripple_color_selector.xml (OK - compressed)

 4208308 res/color/m3_elevated_chip_background_color.xml (OK - compressed)

 4208690 res/color/m3_fab_efab_background_color_selector.xml (OK - compressed)

 4209014 res/color/m3_fab_efab_foreground_color_selector.xml (OK - compressed)

 4209329 res/color/m3_fab_ripple_color_selector.xml (OK - compressed)

 4209721 res/color/m3_filled_icon_button_container_color_selector.xml (OK - compressed)

 4210075 res/color/m3_highlighted_text.xml (OK - compressed)

 4210340 res/color/m3_hint_foreground.xml (OK - compressed)

 4210679 res/color/m3_icon_button_icon_color_selector.xml (OK - compressed)

 4211061 res/color/m3_navigation_bar_item_with_indicator_icon_tint.xml (OK - compressed)

 4211493 res/color/m3_navigation_bar_item_with_indicator_label_tint.xml (OK - compressed)

 4211912 res/color/m3_navigation_bar_ripple_color_selector.xml (OK - compressed)

 4212352 res/color/m3_navigation_item_background_color.xml (OK - compressed)

 4212678 res/color/m3_navigation_item_icon_tint.xml (OK - compressed)

 4213137 res/color/m3_navigation_item_ripple_color.xml (OK - compressed)

 4213589 res/color/m3_navigation_item_text_color.xml (OK - compressed)

 4214065 res/color/m3_navigation_rail_item_with_indicator_icon_tint.xml (OK - compressed)

 4214385 res/color/m3_navigation_rail_item_with_indicator_label_tint.xml (OK - compressed)

 4214697 res/color/m3_navigation_rail_ripple_color_selector.xml (OK - compressed)

 4215127 res/color/m3_popupmenu_overlay_color.xml (OK - compressed)

 4215405 res/color/m3_primary_text_disable_only.xml (OK - compressed)

 4215717 res/color/m3_radiobutton_button_tint.xml (OK - compressed)

 4216188 res/color/m3_radiobutton_ripple_tint.xml (OK - compressed)

 4216653 res/color/m3_selection_control_ripple_color_selector.xml (OK - compressed)

 4217104 res/color/m3_simple_item_ripple_color.xml (OK - compressed)

 4217475 res/color/m3_slider_active_track_color.xml (OK - compressed)

 4217780 res/color/m3_slider_halo_color.xml (OK - compressed)

 4218105 res/color/m3_slider_inactive_track_color.xml (OK - compressed)

 4218412 res/color/m3_slider_thumb_color.xml (OK - compressed)

 4218718 res/color/m3_switch_thumb_tint.xml (OK - compressed)

 4219037 res/color/m3_switch_track_tint.xml (OK - compressed)

 4219371 res/color/m3_tabs_icon_color.xml (OK - compressed)

 4219718 res/color/m3_tabs_icon_color_secondary.xml (OK - compressed)

 4220054 res/color/m3_tabs_ripple_color.xml (OK - compressed)

 4220508 res/color/m3_tabs_ripple_color_secondary.xml (OK - compressed)

 4220944 res/color/m3_tabs_text_color.xml (OK - compressed)

 4221291 res/color/m3_tabs_text_color_secondary.xml (OK - compressed)

 4221647 res/color/m3_text_button_background_color_selector.xml (OK - compressed)

 4221973 res/color/m3_text_button_foreground_color_selector.xml (OK - compressed)

 4222358 res/color/m3_text_button_ripple_color_selector.xml (OK - compressed)

 4222888 res/color/m3_textfield_filled_background_color.xml (OK - compressed)

 4223208 res/color/m3_textfield_indicator_text_color.xml (OK - compressed)

 4223620 res/color/m3_textfield_input_text_color.xml (OK - compressed)

 4223991 res/color/m3_textfield_label_color.xml (OK - compressed)

 4224338 res/color/m3_textfield_stroke_color.xml (OK - compressed)

 4224724 res/color/m3_timepicker_button_background_color.xml (OK - compressed)

 4225046 res/color/m3_timepicker_button_ripple_color.xml (OK - compressed)

 4225519 res/color/m3_timepicker_button_text_color.xml (OK - compressed)

 4225895 res/color/m3_timepicker_clock_text_color.xml (OK - compressed)

 4226206 res/color/m3_timepicker_display_background_color.xml (OK - compressed)

 4226510 res/color/m3_timepicker_display_ripple_color.xml (OK - compressed)

 4226960 res/color/m3_timepicker_display_text_color.xml (OK - compressed)

 4227326 res/color/m3_timepicker_secondary_text_button_ripple_color.xml (OK - compressed)

 4227718 res/color/m3_timepicker_secondary_text_button_text_color.xml (OK - compressed)

 4228047 res/color/m3_timepicker_time_input_stroke_color.xml (OK - compressed)

 4228358 res/color/m3_tonal_button_ripple_color_selector.xml (OK - compressed)

 4228726 res/color/material_cursor_color.xml (OK - compressed)

 4228971 res/color/material_divider_color.xml (OK - compressed)

 4229252 res/color/material_on_background_disabled.xml (OK - compressed)

 4229543 res/color/material_on_background_emphasis_high_type.xml (OK - compressed)

 4229829 res/color/material_on_background_emphasis_medium.xml (OK - compressed)

 4230107 res/color/material_on_primary_disabled.xml (OK - compressed)

 4230389 res/color/material_on_primary_emphasis_high_type.xml (OK - compressed)

 4230668 res/color/material_on_primary_emphasis_medium.xml (OK - compressed)

 4230941 res/color/material_on_surface_disabled.xml (OK - compressed)

 4231225 res/color/material_on_surface_emphasis_high_type.xml (OK - compressed)

 4231506 res/color/material_on_surface_emphasis_medium.xml (OK - compressed)

 4231779 res/color/material_on_surface_stroke.xml (OK - compressed)

 4232068 res/color/material_personalized__highlighted_text.xml (OK - compressed)

 4232361 res/color/material_personalized__highlighted_text_inverse.xml (OK - compressed)

 4232647 res/color/material_personalized_color_primary_text.xml (OK - compressed)

 4232979 res/color/material_personalized_color_primary_text_inverse.xml (OK - compressed)

 4233304 res/color/material_personalized_color_secondary_text.xml (OK - compressed)

 4233642 res/color/material_personalized_color_secondary_text_inverse.xml (OK - compressed)

 4233966 res/color/material_personalized_hint_foreground.xml (OK - compressed)

 4234315 res/color/material_personalized_hint_foreground_inverse.xml (OK - compressed)

 4234673 res/color/material_personalized_primary_inverse_text_disable_only.xml (OK - compressed)

 4235005 res/color/material_personalized_primary_text_disable_only.xml (OK - compressed)

 4235329 res/color/material_slider_active_tick_marks_color.xml (OK - compressed)

 4235660 res/color/material_slider_active_track_color.xml (OK - compressed)

 4235976 res/color/material_slider_halo_color.xml (OK - compressed)

 4236311 res/color/material_slider_inactive_tick_marks_color.xml (OK - compressed)

 4236648 res/color/material_slider_inactive_track_color.xml (OK - compressed)

 4236973 res/color/material_slider_thumb_color.xml (OK - compressed)

 4237302 res/color/material_timepicker_button_background.xml (OK - compressed)

 4237628 res/color/material_timepicker_button_stroke.xml (OK - compressed)

 4237915 res/color/material_timepicker_clock_text_color.xml (OK - compressed)

 4238214 res/color/material_timepicker_clockface.xml (OK - compressed)

 4238498 res/color/material_timepicker_modebutton_tint.xml (OK - compressed)

 4238772 res/color/mtrl_btn_bg_color_selector.xml (OK - compressed)

 4239084 res/color/mtrl_btn_ripple_color.xml (OK - compressed)

 4239479 res/color/mtrl_btn_stroke_color_selector.xml (OK - compressed)

 4239810 res/color/mtrl_btn_text_btn_bg_color_selector.xml (OK - compressed)

 4240133 res/color/mtrl_btn_text_btn_ripple_color.xml (OK - compressed)

 4240528 res/color/mtrl_btn_text_color_selector.xml (OK - compressed)

 4240850 res/color/mtrl_calendar_item_stroke_color.xml (OK - compressed)

 4241238 res/color/mtrl_calendar_selected_range.xml (OK - compressed)

 4241513 res/color/mtrl_card_view_foreground.xml (OK - compressed)

 4241887 res/color/mtrl_card_view_ripple.xml (OK - compressed)

 4242271 res/color/mtrl_chip_background_color.xml (OK - compressed)

 4242655 res/color/mtrl_chip_close_icon_tint.xml (OK - compressed)

 4243071 res/color/mtrl_chip_surface_color.xml (OK - compressed)

 4243315 res/color/mtrl_chip_text_color.xml (OK - compressed)

 4243648 res/color/mtrl_choice_chip_background_color.xml (OK - compressed)

 4244037 res/color/mtrl_choice_chip_ripple_color.xml (OK - compressed)

 4244434 res/color/mtrl_choice_chip_text_color.xml (OK - compressed)

 4244797 res/color/mtrl_error.xml (OK - compressed)

 4245095 res/color/mtrl_fab_bg_color_selector.xml (OK - compressed)

 4245419 res/color/mtrl_fab_icon_text_color_selector.xml (OK - compressed)

 4245732 res/color/mtrl_fab_ripple_color.xml (OK - compressed)

 4246125 res/color/mtrl_filled_background_color.xml (OK - compressed)

 4246500 res/color/mtrl_filled_icon_tint.xml (OK - compressed)

 4246854 res/color/mtrl_filled_stroke_color.xml (OK - compressed)

 4247244 res/color/mtrl_indicator_text_color.xml (OK - compressed)

 4247578 res/color/mtrl_navigation_bar_colored_item_tint.xml (OK - compressed)

 4247908 res/color/mtrl_navigation_bar_colored_ripple_color.xml (OK - compressed)

 4248312 res/color/mtrl_navigation_bar_item_tint.xml (OK - compressed)

 4248638 res/color/mtrl_navigation_bar_ripple_color.xml (OK - compressed)

 4249137 res/color/mtrl_navigation_item_background_color.xml (OK - compressed)

 4249488 res/color/mtrl_navigation_item_icon_tint.xml (OK - compressed)

 4249843 res/color/mtrl_navigation_item_text_color.xml (OK - compressed)

 4250211 res/color/mtrl_on_primary_text_btn_text_color_selector.xml (OK - compressed)

 4250526 res/color/mtrl_on_surface_ripple_color.xml (OK - compressed)

 4250897 res/color/mtrl_outlined_icon_tint.xml (OK - compressed)

 4251254 res/color/mtrl_outlined_stroke_color.xml (OK - compressed)

 4251647 res/color/mtrl_popupmenu_overlay_color.xml (OK - compressed)

 4251924 res/color/mtrl_switch_thumb_icon_tint.xml (OK - compressed)

 4252364 res/color/mtrl_switch_thumb_tint.xml (OK - compressed)

 4252777 res/color/mtrl_switch_track_decoration_tint.xml (OK - compressed)

 4253121 res/color/mtrl_switch_track_tint.xml (OK - compressed)

 4253566 res/color/mtrl_tabs_colored_ripple_color.xml (OK - compressed)

 4253970 res/color/mtrl_tabs_icon_color_selector.xml (OK - compressed)

 4254294 res/color/mtrl_tabs_icon_color_selector_colored.xml (OK - compressed)

 4254614 res/color/mtrl_tabs_legacy_text_color_selector.xml (OK - compressed)

 4254908 res/color/mtrl_tabs_ripple_color.xml (OK - compressed)

 4255403 res/color/mtrl_text_btn_text_color_selector.xml (OK - compressed)

 4255796 res/color/switch_thumb_material_dark.xml (OK - compressed)

 4256096 res/color/switch_thumb_material_light.xml (OK - compressed)

 4256365 res/cv.xml (OK - compressed)

 4257344 res/cy.xml (OK - compressed)

 4257708 res/d2.webp (OK)

 4260188 res/d2.xml (OK - compressed)

 4260440 res/d3.png (OK)

 4260916 res/d5.9.png (OK)

 4261807 res/dC.xml (OK - compressed)

 4262160 res/dO.xml (OK - compressed)

 4263302 res/dS.xml (OK - compressed)

 4263960 res/dW.png (OK)

 4264268 res/dY.png (OK)

 4264712 res/df.xml (OK - compressed)

 4265006 res/dj.xml (OK - compressed)

 4265366 res/dw.xml (OK - compressed)

 4266030 res/e0.xml (OK - compressed)

 4266390 res/eA.xml (OK - compressed)

 4267140 res/eH.xml (OK - compressed)

 4267566 res/eH1.xml (OK - compressed)

 4267966 res/eK.xml (OK - compressed)

 4268261 res/eK1.xml (OK - compressed)

 4268613 res/eM.xml (OK - compressed)

 4268900 res/eR.png (OK)

 4269300 res/eT.9.png (OK)

 4269658 res/eZ.xml (OK - compressed)

 4269992 res/ec.xml (OK - compressed)

 4270540 res/ej.9.png (OK)

 4270747 res/ej.xml (OK - compressed)

 4271044 res/ev.9.png (OK)

 4271312 res/f6.xml (OK - compressed)

 4271656 res/fM.9.png (OK)

 4272521 res/fW.xml (OK - compressed)

 4272928 res/f_.xml (OK - compressed)

 4273290 res/fd.xml (OK - compressed)

 4273682 res/fg.xml (OK - compressed)

 4274068 res/fp.xml (OK - compressed)

 4274324 res/fq.webp (OK)

 4280692 res/fu.xml (OK - compressed)

 4281292 res/g-.png (OK)

 4281917 res/g3.xml (OK - compressed)

 4282132 res/gC.xml (OK - compressed)

 4282530 res/gD.xml (OK - compressed)

 4282813 res/gG.xml (OK - compressed)

 4283380 res/gK.9.png (OK)

 4283635 res/gR.xml (OK - compressed)

 4283896 res/gZ.9.png (OK)

 4284464 res/gj.9.png (OK)

 4284744 res/gt.9.png (OK)

 4284989 res/h4.xml (OK - compressed)

 4285672 res/h7.9.png (OK)

 4286808 res/hP.xml (OK - compressed)

 4287294 res/hP1.xml (OK - compressed)

 4287952 res/hZ.9.png (OK)

 4288159 res/hb.xml (OK - compressed)

 4288619 res/hc.xml (OK - compressed)

 4288984 res/hh.9.png (OK)

 4289236 res/hq.xml (OK - compressed)

 4289609 res/hu.xml (OK - compressed)

 4290016 res/hv.xml (OK - compressed)

 4290468 res/i6.9.png (OK)

 4290696 res/iE.webp (OK)

 4337230 res/iI.xml (OK - compressed)

 4337456 res/iO.png (OK)

 4337724 res/iO.xml (OK - compressed)

 4338293 res/iQ.xml (OK - compressed)

 4338756 res/iR.9.png (OK)

 4339280 res/iZ.xml (OK - compressed)

 4340446 res/iZ1.xml (OK - compressed)

 4340892 res/iZ2.xml (OK - compressed)

 4341116 res/io.9.png (OK)

 4342682 res/j3.xml (OK - compressed)

 4343076 res/j4.png (OK)

 4343904 res/jS.9.png (OK)

 4344184 res/jS1.9.png (OK)

 4344392 res/jW.png (OK)

 4345188 res/j_.webp (OK)

 4363496 res/je.9.png (OK)

 4363748 res/k0.xml (OK - compressed)

 4364460 res/k8.xml (OK - compressed)

 4365153 res/k9.xml (OK - compressed)

 4365480 res/kJ.9.png (OK)

 4365728 res/kN.xml (OK - compressed)

 4365991 res/k_.xml (OK - compressed)

 4366289 res/kg.xml (OK - compressed)

 4366766 res/kh.xml (OK - compressed)

 4367034 res/kj.xml (OK - compressed)

 4367805 res/kn.xml (OK - compressed)

 4368184 res/kp.png (OK)

 4368448 res/lE.xml (OK - compressed)

 4368814 res/lN.xml (OK - compressed)

 4369048 res/lP.9.png (OK)

 4369282 res/lR.xml (OK - compressed)

 4369581 res/lv.xml (OK - compressed)

 4370068 res/ly.png (OK)

 4370380 res/m0.png (OK)

 4370687 res/mA.xml (OK - compressed)

 4371216 res/mm.9.png (OK)

 4371601 res/mt.xml (OK - compressed)

 4371886 res/n0.xml (OK - compressed)

 4372208 res/nI.9.png (OK)

 4372455 res/nL.xml (OK - compressed)

 4372742 res/nT.xml (OK - compressed)

 4373188 res/nX.xml (OK - compressed)

 4373608 res/nf.png (OK)

 4374856 res/nl.xml (OK - compressed)

 4375431 res/nm.xml (OK - compressed)

 4375782 res/no.xml (OK - compressed)

 4376175 res/nu.xml (OK - compressed)

 4376512 res/nz.xml (OK - compressed)

 4376808 res/o9.9.png (OK)

 4377108 res/oP.xml (OK - compressed)

 4377490 res/oY.xml (OK - compressed)

 4378136 res/o_.9.png (OK)

 4378428 res/o_.png (OK)

 4379119 res/oa.xml (OK - compressed)

 4379508 res/op.9.png (OK)

 4379834 res/p0.xml (OK - compressed)

 4380529 res/pF.xml (OK - compressed)

 4380946 res/pF1.xml (OK - compressed)

 4381230 res/pU.xml (OK - compressed)

 4381488 res/pY.png (OK)

 4381636 res/pk.png (OK)

 4381951 res/pn.xml (OK - compressed)

 4382312 res/ps.9.png (OK)

 4382530 res/pw.xml (OK - compressed)

 4383076 res/py.9.png (OK)

 4383333 res/q6.xml (OK - compressed)

 4383768 res/qA.xml (OK - compressed)

 4384076 res/qD.9.png (OK)

 4386952 res/qp.png (OK)

 4387192 res/qs.webp (OK)

 4393924 res/qx.xml (OK - compressed)

 4394706 res/qz.xml (OK - compressed)

 4395177 res/rI.xml (OK - compressed)

 4395598 res/rJ.xml (OK - compressed)

 4395972 res/rW.xml (OK - compressed)

 4396317 res/rY.xml (OK - compressed)

 4396654 res/rd.xml (OK - compressed)

 4397000 res/rj.9.png (OK)

 4398138 res/rx.xml (OK - compressed)

 4398478 res/rz.xml (OK - compressed)

 4398748 res/s0.png (OK)

 4399196 res/s3.9.png (OK)

 4399484 res/s4.png (OK)

 4399624 res/sA.9.png (OK)

 4399876 res/sA.xml (OK - compressed)

 4400148 res/sK.webp (OK)

 4422484 res/sO.xml (OK - compressed)

 4422888 res/sS.xml (OK - compressed)

 4423207 res/sX.xml (OK - compressed)

 4423536 res/sg.9.png (OK)

 4423802 res/sl.xml (OK - compressed)

 4424275 res/sn.xml (OK - compressed)

 4424529 res/t8.xml (OK - compressed)

 4424892 res/tG.png (OK)

 4425408 res/tI.xml (OK - compressed)

 4425703 res/tL.xml (OK - compressed)

 4426068 res/tS.png (OK)

 4426429 res/tS.xml (OK - compressed)

 4426772 res/tU.9.png (OK)

 4427024 res/tZ.9.png (OK)

 4427264 res/te.png (OK)

 4427582 res/te.xml (OK - compressed)

 4427828 res/tp.xml (OK - compressed)

 4428124 res/u0.xml (OK - compressed)

 4428564 res/u3.png (OK)

 4429120 res/u5.webp (OK)

 4439010 res/uJ.xml (OK - compressed)

 4439488 res/uL.9.png (OK)

 4440494 res/uR.xml (OK - compressed)

 4441054 res/ua.xml (OK - compressed)

 4441392 res/uj.9.png (OK)

 4441632 res/ut.9.png (OK)

 4442428 res/uu.9.png (OK)

 4442672 res/v-.xml (OK - compressed)

 4443188 res/v4.9.png (OK)

 4443436 res/v9.xml (OK - compressed)

 4444525 res/vG.xml (OK - compressed)

 4445163 res/vH.xml (OK - compressed)

 4445498 res/vJ.xml (OK - compressed)

 4445824 res/vL.9.png (OK)

 4446087 res/vR.xml (OK - compressed)

 4446352 res/vT.xml (OK - compressed)

 4446580 res/vZ.xml (OK - compressed)

 4446837 res/vf.xml (OK - compressed)

 4447166 res/vl.xml (OK - compressed)

 4447733 res/vq.xml (OK - compressed)

 4448309 res/vr.xml (OK - compressed)

 4448684 res/vz.9.png (OK)

 4448905 res/vz.xml (OK - compressed)

 4449153 res/w9.xml (OK - compressed)

 4449428 res/wL.9.png (OK)

 4449908 res/wN.9.png (OK)

 4450173 res/wP.xml (OK - compressed)

 4450528 res/w_.png (OK)

 4450784 res/x3.9.png (OK)

 4451036 res/xH.png (OK)

 4451262 res/xN.xml (OK - compressed)

 4452090 res/xQ.xml (OK - compressed)

 4452580 res/xR.9.png (OK)

 4452816 res/xa.9.png (OK)

 4453077 res/xa.xml (OK - compressed)

 4453639 res/xd.xml (OK - compressed)

 4454356 res/xj.xml (OK - compressed)

 4454648 res/xo.xml (OK - compressed)

 4454933 res/y-.xml (OK - compressed)

 4455201 res/y4.xml (OK - compressed)

 4455460 res/yH.9.png (OK)

 4455684 res/yO.xml (OK - compressed)

 4455934 res/yT.xml (OK - compressed)

 4456257 res/yV.xml (OK - compressed)

 4456596 res/yY.9.png (OK)

 4456822 res/yY.xml (OK - compressed)

 4457107 res/ya.xml (OK - compressed)

 4457468 res/yg.9.png (OK)

 4457904 res/yw.webp (OK)

 4461540 res/z-.9.png (OK)

 4461804 res/z9.9.png (OK)

 4462264 res/zE.png (OK)

 4462628 res/zG.xml (OK - compressed)

 4462948 res/zR.xml (OK - compressed)

 4463444 res/zV.9.png (OK)

 4465947 res/zc.xml (OK - compressed)

 4466397 res/zp.xml (OK - compressed)

 4466656 res/zq.xml (OK - compressed)

 4466972 resources.arsc (OK)

 5421901 AndroidManifest.xml (OK - compressed)

 5423642 assets/virtual (OK - compressed)

 5423716 assets/mode (OK - compressed)

 5423798 lib/arm64-v8a/libegis.so (OK - compressed)

 5889714 lib/x86_64/libegis-x86.so (OK - compressed)

 6400428 lib/x86/libegis-x86.so (OK - compressed)

 6915780 lib/armeabi-v7a/libegis.so (OK - compressed)

 7373494 classes.dex (OK - compressed)

Verification succesful


07/30/2025 15:14:00 [DEBUG] [filename:execute_cmd.py] [line:50] 
07/30/2025 15:14:00 [DEBUG] [filename:apk.py] [line:217] zipalign D:\work\AegisShield\release-4.11.1\src\AppShield_TopGrade\91c18af0aca0af9c\7379dd241496c4ed.apk succeed!
07/30/2025 15:14:00 [INFO] [filename:main.py] [line:1540] total protected number is 1
07/30/2025 15:14:00 [INFO] [filename:main.py] [line:1541] the totall time is 9
07/30/2025 15:14:00 [INFO] [filename:main.py] [line:1542] =============Congratulations!======================
07/30/2025 15:14:00 [INFO] [filename:main.py] [line:1543] generate the protected apk successed!
07/30/2025 15:14:00 [INFO] [filename:main.py] [line:1544] payegis 4.11.1 finished!
