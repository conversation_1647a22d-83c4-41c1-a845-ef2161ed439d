#!/usr/bin/env python
# -*- coding: utf-8 -*-
# <AUTHOR> <PERSON>

import argparse
from optparse import OptionGroup

def handle_args():
    parser = argparse.ArgumentParser(usage="main.py <SOURCE.APK> [TARGET.APK|TARGET.diff]")
    """
    与加固功能无关,只是我们内部使用的选项
    """
    parser.add_argument("-v",
                        action="store_true",
                        default=False,
                        dest='verbose',
                        help='the switch to the verbose')

    parser.add_argument("--store-tmp",
                        action="store_true",
                        default=False,
                        dest='store_tmp',
                        help='the switch to decide whether store the tmp date')

    parser.add_argument("--apktool-modifty-xml",
                        action="store_true",
                        default=False,
                        dest='apktool_modifty_xml',
                        help='the switch to decide use apktool or ameditor')
    
    parser.add_argument("--get-mode",
                        action="store_true",
                        default=False,
                        dest='get_mode',
                        help='gets all protection parameters for the app')
    """
    默认开启的功能，主要用于开发时进行配置
    """
    parser.add_argument("--upx",
                        action="store_true",
                        default=True,
                        dest='upx',
                        help='the switch to the upx')

    parser.add_argument("--protect-so",
                        action="store_true",
                        default=True,
                        dest='protect_so',
                        help='we use this mandatorily because we need to encrypt some codes in libegis.so')
    # 对生成的apk进行对齐操作,以便在Android 10 上安装
    parser.add_argument("--zipalign",
                        action="store_true",
                        default=True,
                        dest='zipalign',
                        help='the switch to zipalign the apk')
    # JAVA层防调试,默认开启
    parser.add_argument("--antidebug",
                        action="store_true",
                        default=True,
                        dest='antidebug',
                        help='the switch to antidebug')
    """
    标准版的功能
    """
    # VMP Instructions
    parser.add_argument("--jade",
                        action="store_true",
                        default=False,
                        dest='enable_jade',
                        help='the switch to the VMP')
    # 防模拟器
    parser.add_argument("--anti-emulator",
                        action="store_true",
                        default=False,
                        dest='anti_emulator',
                        help='whether the protected app is allowed running in an emulator')
    # 反调试（双进程）
    parser.add_argument("--anti-debug-two-process",
                        action="store_true",
                        default=False,
                        dest='anti_debug_two_process',
                        help='Use two processes to anti injection')
    # 日志过滤
    parser.add_argument("--log-block",
                        action="store_true",
                        default=False,
                        dest="log_block",
                        help="remove debug logs")
    # 反界面劫持
    parser.add_argument("--anti-activity-hijack",
                        action="store_true",
                        default=False,
                        dest="anti_activity_hijack",
                        help="protect our activities in case of being hijacked")
    # root检测(toast)
    parser.add_argument("--toast-root-check",
                        action="store_true",
                        default=False,
                        dest="toast_root_check",
                        help="the switch to check users telephone whether rooted or not ")
    # AndroidManifest.xml防读取
    parser.add_argument("--string-xml-protect",
                        action="store_true",
                        default=False,
                        dest="string_xml_protect",
                        help="Protect AndroidManifest.xml to defend tools like apktool")
    # 签名校验
    parser.add_argument("--signature-check",
                        action="store_true",
                        default=False,
                        dest="signature_check",
                        help="the switch to check app signature is correct")
    # 文件防篡改 （资源、dex、so等）
    parser.add_argument("--file-check",
                        action="store_true",
                        default=False,
                        dest='file_check',
                        help='the switch to the file check(res/assets/xml...)')
    """
    增强版中的功能
    """
    # 定制化VMP
    parser.add_argument("--customized-jade",
                        action="store_true",
                        default=False,
                        dest="customized_jade",
                        help="Use annotations to tell us which classes or methods should be protected by vmp")
    
    # 代理检测
    parser.add_argument("--proxy-detection",
                        action="store_true",
                        default=False,
                        dest="proxy_detection",
                        help="Check if the mobile phone sets the proxy address, if so, we will popup a toast")
    # 代理阻断
    parser.add_argument("--proxy-block",
                        action="store_true",
                        default=False,
                        dest="proxy_block",
                        help="Check if the mobile phone sets the proxy address, if so, we will kill the app")
    # 反截屏录屏
    parser.add_argument("--anti-screenshot",
                        action="store_true",
                        default=False,
                        dest='anti_screenshot',
                        help='Screenshot will be forbidden')
    # 本地数据保护
    parser.add_argument("--shared-preference",
                        action="store_true",
                        default=False,
                        dest='shared_preference',
                        help='Protecting files under /data/data/pkg_name/shared_prefs')
    # sqlite数据库加密
    parser.add_argument("--sqlite-encrypt",
                        action="store_true",
                        default=False,
                        dest='sqlite_encrypt',
                        help='Protecting files under /data/data/pkg_name/database')
    # 本地缓存加密
    parser.add_argument("--cache-encrypt",
                        action="store_true",
                        default=False,
                        dest='cache_encrypt',
                        help='Protecting files under /data/data/pkg_name/cache')
    # 本地webview加密
    parser.add_argument("--webview-encrypt",
                        action="store_true",
                        default=False,
                        dest='webview_encrypt',
                        help='Protecting files under /data/data/pkg_name/app_webview')
    # 资源加密不落地解密
    parser.add_argument("--res-encrypt",
                        action="store_true",
                        default=False,
                        dest='res_encrypt',
                        help='Protecting files under /assets and /res')
    # 资源加密落地解密
    parser.add_argument("--res-encrypt-landing",
                        action="store_true",
                        default=False,
                        dest='res_encrypt_landing',
                        help='Protecting files under /assets and /res')

    # 资源加密不落地解密-仅RES
    parser.add_argument("--res-encrypt2",
                        action="store_true",
                        default=False,
                        dest='res_encrypt2',
                        help='Protecting files under /res')

    # 反调试（占用进程）
    parser.add_argument("--anti-debug-process",
                        action="store_true",
                        default=False,
                        dest='anti_debug_process',
                        help='Use processes to anti injection')           
    # 反xposed
    parser.add_argument("--anti-xposed",
                        action="store_true",
                        default=False,
                        dest='anti_xposed',
                        help='the switch to anti toolkits like xposed...')
    # 反virtualAPP
    parser.add_argument("--anti-sandbox",
                        action="store_true",
                        default=False,
                        dest='anti_sandbox',
                        help='the switch to anti toolkits like virtualapp...')
    # 第三方so保护
    parser.add_argument("--thirdparty-so-protect",
                        action="store",
                        default=None,
                        dest='thirdparty_so_protect',
                        help='the switch to the thirdparty-so-protect')
    # 第三方so绑定
    parser.add_argument("--thirdparty-so-binding",
                        action="store_true",
                        default=False,
                        dest="thirdparty_so_binding",
                        help="the switch to the thirdparty-so-binding")
    # 剪贴板保护
    parser.add_argument("--anti-clipboard",
                        action="store_true",
                        default=False,
                        dest='anti_clipboard',
                        help='can not copy/paste anymore when using this...')
    # js混淆
    parser.add_argument("--js_encrypt",
                        action="store_true",
                        default=False,
                        dest='js_encrypt',
                        help='the switch to the js_encrypt')
    # 多渠道打包
    parser.add_argument("--packer",
                        action="store",
                        dest='packer_argument',
                        help='the list of markets')
    # 多渠道打包new
    parser.add_argument("--channel-packer",
                        action="store",
                        dest='channel_packer',
                        help='the list of markets')
    # root检测(反射调用)
    parser.add_argument("--root-check-passiv",
                        action="store_true",
                        default=False,
                        dest="root_check_passiv",
                        help="the switch to check users telephone whether rooted or not ")
    # 反界面劫持(反射调用)
    parser.add_argument("--anti-hijack-passiv",
                        action="store_true",
                        default=False,
                        dest="anti_hijack_passiv",
                        help="protect our activities in case of being hijacked ")
    # frida检测
    parser.add_argument("--anti-frida",
                        action="store_true",
                        default=False,
                        dest="anti_frida",
                        help="When frida is detected, we shut down the app directly")
    # 防御自动脱壳机
    parser.add_argument("--anti-unpack",
                        action="store_true",
                        default=False,
                        dest="anti_unpack",
                        help="Anti automatic unshell tools like customized android system")
    # 内存数据防读取
    parser.add_argument("--guard-memory",
                        action="store_true",
                        default=False,
                        dest="guard_memory",
                        help="Anti dump or anti malicious momery manipulation")
    # DEX拆分
    parser.add_argument("--split-dex",
                      action="store_true",
                      default=False,
                      dest="split_dex",
                      help="Add DEX limit max value process support")
    # 检测magisk
    # parser.add_argument("--magisk-check",
    #                   action="store_true",
    #                   default=False,
    #                   dest="magisk_check",
    #                   help="When magisk is detected, we need pop toast")
    # 检测虚拟位置提供者
    parser.add_argument("--fake-location",
                        action="store_true",
                        default=False,
                        dest="fake_location",
                        help="the switch to check if location is fake")
    # 检测VPN
    parser.add_argument("--vpn-check",
                      action="store_true",
                      default=False,
                      dest="vpn_check",
                      help="When VPN service is detected, we need kill app")
    # 安全分享
    parser.add_argument("--safe-share",
                      action="store",
                      default=None,
                      dest="safe_sharing",
                      help="safe sharing, sharing filter")
	# 自适应VMP性能模式
    parser.add_argument("--ajp-mode",
                      action="store_true",
                      default=False,
                      dest="ajp_mode",
                      help="Adaptive VMP Performance Mode")
    # 过滤模式
    parser.add_argument("--filter-mode",
                      action="store_true",
                      default=False,
                      dest="filter_mode",
                      help="Only protect to APK Activities and Fragments")
   # Unity3D保护
    parser.add_argument("--u3d-protect",
                      action="store_true",
                      default=False,
                      dest="unity3d_protect",
                      help="protect unity3d app core files")

    # ROOT检测，停止进程
    parser.add_argument("--block-root-check",
                      action="store_true",
                      default=False,
                      dest="block_root_check",
                      help="Prevent the application from running on the root device.")
    
    # 签名校验，停止进程
    parser.add_argument("--block-signature-check",
                      action="store_true",
                      default=False,
                      dest="block_signature_check",
                      help="It is forbidden to run apps with inconsistent signatures.")

    # 壳优化
    parser.add_argument("--shell-opt",
                      action="store_true",
                      default=False,
                      dest="shell_opt",
                      help="shell optimization 'interfaceR()'")

    # 手机模拟点击检测(无障碍AccessibilityService)
    parser.add_argument("--as-check",
                      action="store_true",
                      default=False,
                      dest="as_check",
                      help="When AccessibilityService is detected, we shut down the app directly")

    # group = OptionGroup(parser, "Example", "python main.py C:\\source.apk D:\\out.apk")
    return parser
