// auto generate header
#ifndef DALVIK_OPMAP_H_
#define DALVIK_OPMAP_H_
const static unsigned char origOpToNewOp[256] = {0x9e, 0xd8, 0x94, 0x11, 0xc6, 0xcb, 0x99, 0xc7, 0xb3, 0x3c, 0x72, 0x33, 0xf2, 0xf9, 0x0d, 0x38, 0x88, 0x5c, 0xdb, 0x47, 0xa7, 0x53, 0xd7, 0x57, 0xf1, 0xf3, 0x7f, 0xec, 0xb0, 0xe1, 0x3e, 0xda, 0x1d, 0x08, 0x39, 0x49, 0x6f, 0xb5, 0xfe, 0xb9, 0x70, 0xd4, 0x1c, 0x5f, 0x01, 0xea, 0xc4, 0xe7, 0x16, 0xf8, 0x4b, 0xac, 0x34, 0x7b, 0x07, 0x0f, 0x17, 0x43, 0x0c, 0xe8, 0x12, 0x78, 0x52, 0x32, 0xa9, 0x6c, 0x60, 0xd5, 0xfd, 0xfc, 0x5d, 0xb2, 0x50, 0xd9, 0x7e, 0x9d, 0x7c, 0x42, 0x95, 0x66, 0xc3, 0xcc, 0xa3, 0xdd, 0x93, 0xb6, 0x80, 0xc2, 0x31, 0x36, 0xad, 0x6a, 0x13, 0xca, 0xc5, 0x6e, 0xa1, 0x26, 0x83, 0xe0, 0x9a, 0xd1, 0xb1, 0xee, 0x06, 0x18, 0x68, 0x8d, 0x29, 0xff, 0x19, 0x21, 0x92, 0x03, 0xcf, 0x69, 0x48, 0x81, 0x87, 0x3d, 0xb4, 0xf4, 0x98, 0x20, 0x61, 0x71, 0x0e, 0x6b, 0xf0, 0xcd, 0xc0, 0x59, 0x8e, 0x75, 0x85, 0x82, 0x86, 0x4f, 0x2c, 0x76, 0x8f, 0xe4, 0xfb, 0xa5, 0x2d, 0xf6, 0xeb, 0xe3, 0x24, 0xbb, 0x51, 0x10, 0xd3, 0xe2, 0xdc, 0x27, 0x4e, 0x8b, 0x23, 0x63, 0xa4, 0xae, 0x46, 0xc8, 0x65, 0xba, 0xd6, 0x62, 0x15, 0x4c, 0x35, 0x2f, 0x09, 0x1b, 0x74, 0xa2, 0x97, 0xb8, 0xa6, 0x89, 0xbe, 0x0a, 0xbf, 0x84, 0x28, 0x0b, 0xaa, 0x8a, 0x40, 0xbc, 0xf5, 0x3b, 0xaf, 0x58, 0x04, 0x7d, 0x2a, 0x25, 0xce, 0x37, 0x1f, 0x1a, 0xc9, 0xb7, 0x5b, 0x5e, 0xed, 0xe5, 0x6d, 0xdf, 0x41, 0x5a, 0x56, 0x8c, 0x54, 0xde, 0x4d, 0x55, 0xfa, 0xc1, 0xa0, 0x02, 0x3a, 0x22, 0x44, 0x45, 0xe9, 0xab, 0x77, 0x9f, 0xbd, 0xe6, 0xd0, 0xef, 0x2b, 0xa8, 0x79, 0x73, 0x9b, 0x05, 0x96, 0x30, 0x91, 0x9c, 0x2e, 0xf7, 0x67, 0x00, 0x90, 0x4a, 0xd2, 0x64, 0x1e, 0x3f, 0x7a, 0x14};
#endif
