// auto generate header
#ifndef DALVIK_OPMAP_H_
#define DALVIK_OPMAP_H_
const static unsigned char origOpToNewOp[256] = {0x86, 0x8b, 0x91, 0x94, 0xc5, 0x29, 0x30, 0x32, 0xee, 0xba, 0xdb, 0x2f, 0x24, 0x62, 0xa9, 0xb2, 0xcc, 0xcd, 0x7c, 0xc4, 0x92, 0x87, 0x4b, 0x4d, 0xb8, 0xcf, 0x80, 0x9b, 0x8c, 0x34, 0x49, 0xc7, 0x01, 0x51, 0x7e, 0xda, 0x0b, 0xd9, 0xbe, 0x6c, 0x79, 0x67, 0x1f, 0x37, 0xde, 0x9c, 0x5f, 0xf6, 0x8f, 0x40, 0x61, 0x60, 0xbb, 0x25, 0x8a, 0x15, 0x6a, 0xa2, 0x54, 0xf5, 0xd4, 0x14, 0x96, 0x0e, 0x73, 0x47, 0x99, 0x28, 0x84, 0x9d, 0x89, 0xf8, 0xc1, 0xb9, 0x6e, 0x7b, 0xd0, 0xd2, 0xe9, 0x3d, 0x76, 0xff, 0xe7, 0xe1, 0x00, 0xbf, 0xc9, 0xe0, 0xac, 0x8e, 0xce, 0xb4, 0x11, 0x88, 0x2a, 0xa4, 0xfe, 0x77, 0xe2, 0xcb, 0xbd, 0x2d, 0x43, 0xf2, 0x08, 0xe3, 0x02, 0xae, 0xf4, 0x07, 0x9a, 0x6f, 0x42, 0x19, 0xa7, 0xa0, 0x97, 0x52, 0x1c, 0x0d, 0x59, 0x4e, 0xb1, 0xfd, 0xb7, 0x22, 0x31, 0x56, 0xf0, 0x7a, 0x36, 0x1b, 0x46, 0xc6, 0xd6, 0x3c, 0x98, 0x3b, 0xa1, 0xca, 0x58, 0x81, 0xf9, 0x21, 0x5c, 0x38, 0x1e, 0x74, 0x6b, 0x53, 0x12, 0x23, 0x4c, 0xf1, 0x57, 0xa8, 0x83, 0xe5, 0x82, 0x04, 0xc8, 0x48, 0xec, 0x0a, 0xb0, 0x3a, 0x72, 0x70, 0x78, 0x90, 0x93, 0x18, 0x16, 0x06, 0xd7, 0x3e, 0x33, 0xd8, 0xa6, 0xaa, 0xc2, 0x4f, 0xdc, 0x5e, 0xc0, 0x27, 0x45, 0x13, 0x39, 0x95, 0x63, 0x05, 0xd3, 0xfb, 0x8d, 0xfa, 0xfc, 0xf3, 0x55, 0x44, 0x0c, 0xeb, 0x66, 0xaf, 0x26, 0x50, 0x65, 0x5b, 0x9e, 0x3f, 0x20, 0x2b, 0x41, 0xa3, 0x1a, 0xe6, 0xbc, 0xdf, 0xb5, 0xe8, 0xea, 0xab, 0xc3, 0xed, 0x03, 0xe4, 0x4a, 0x1d, 0x2e, 0x6d, 0x9f, 0xdd, 0x17, 0x10, 0xef, 0x5d, 0x7f, 0x68, 0xb6, 0xad, 0xf7, 0x09, 0xd1, 0x75, 0x71, 0x69, 0x7d, 0x5a, 0xd5, 0x0f, 0x64, 0x85, 0xa5, 0x2c, 0x35, 0xb3};
#endif
